"""
Basic Service Plugin Template

This template demonstrates a simple service plugin with proper docstring convention.
Perfect for AI agents learning the Plugginger framework basics.
"""

from plugginger.api import plugin, service, PluginBase


@plugin(name="basic_service", version="1.0.0")
class BasicServicePlugin(PluginBase):
    """Basic service plugin demonstrating Plugginger docstring convention."""

    @service()
    async def hello(self) -> str:
        """Hello service

        Returns a greeting message from the plugin.
        
        Returns:
            str: Greeting message from the plugin
            
        Example:
            >>> result = await plugin.hello()
            >>> print(result)
            "Hello from Basic Service Plugin!"
            
        AI_METADATA:
            complexity: low
            dependencies: []
            side_effects: none
            idempotent: true
            async_safe: true
        """
        return "Hello from Basic Service Plugin!"

    @service()
    async def echo(self, message: str) -> str:
        """Echo service

        Returns the input message with a prefix to demonstrate
        parameter handling and string processing.
        
        Args:
            message: Input message to echo back
            
        Returns:
            str: Echoed message with "Echo: " prefix
            
        Raises:
            ValueError: When message is empty or None
            
        Example:
            >>> result = await plugin.echo("Hello World")
            >>> print(result)
            "Echo: Hello World"
            
        AI_METADATA:
            complexity: low
            dependencies: []
            side_effects: none
            idempotent: true
            async_safe: true
        """
        if not message:
            raise ValueError("Message cannot be empty")
        
        return f"Echo: {message}"

    @service()
    async def get_info(self) -> dict[str, str]:
        """Get Info service

        Returns plugin information including name, version, and capabilities.
        Useful for service discovery and health checks.
        
        Returns:
            dict[str, str] containing:
                - name: Plugin name
                - version: Plugin version
                - type: Plugin type classification
                - status: Current plugin status
                
        Example:
            >>> result = await plugin.get_info()
            >>> print(result["name"])
            "basic_service"
            
        AI_METADATA:
            complexity: low
            dependencies: []
            side_effects: none
            idempotent: true
            async_safe: true
        """
        return {
            "name": "basic_service",
            "version": "1.0.0",
            "type": "utility",
            "status": "active"
        }
