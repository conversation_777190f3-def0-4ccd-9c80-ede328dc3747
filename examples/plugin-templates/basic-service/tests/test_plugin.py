"""
Tests for Basic Service Plugin Template

Demonstrates comprehensive testing patterns for Plugginger plugins
with proper docstring validation and service testing.
"""

import pytest
from plugginger.api import PluggingerA<PERSON><PERSON>uilder
from plugginger.core.docstring_convention import Doc<PERSON>ringParser
from plugginger.core.exceptions import ValidationError

from ..plugin import BasicServicePlugin


class TestBasicServicePlugin:
    """Test suite for BasicServicePlugin."""

    @pytest.fixture
    async def app(self):
        """Create test app with BasicServicePlugin."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicServicePlugin)
        return builder.build()

    @pytest.mark.asyncio
    async def test_hello_service(self, app):
        """Test hello service returns correct greeting."""
        result = await app.call_service("basic_service.hello")
        assert result == "Hello from Basic Service Plugin!"
        assert isinstance(result, str)

    @pytest.mark.asyncio
    async def test_echo_service_valid_input(self, app):
        """Test echo service with valid input."""
        test_message = "Hello World"
        result = await app.call_service("basic_service.echo", message=test_message)
        
        assert result == f"Echo: {test_message}"
        assert result.startswith("Echo: ")

    @pytest.mark.asyncio
    async def test_echo_service_empty_message(self, app):
        """Test echo service raises error for empty message."""
        with pytest.raises(ValueError, match="Message cannot be empty"):
            await app.call_service("basic_service.echo", message="")

    @pytest.mark.asyncio
    async def test_echo_service_none_message(self, app):
        """Test echo service raises error for None message."""
        with pytest.raises(ValueError, match="Message cannot be empty"):
            await app.call_service("basic_service.echo", message=None)

    @pytest.mark.asyncio
    async def test_get_info_service(self, app):
        """Test get_info service returns correct plugin information."""
        result = await app.call_service("basic_service.get_info")
        
        assert isinstance(result, dict)
        assert result["name"] == "basic_service"
        assert result["version"] == "1.0.0"
        assert result["type"] == "utility"
        assert result["status"] == "active"
        
        # Verify all expected keys are present
        expected_keys = {"name", "version", "type", "status"}
        assert set(result.keys()) == expected_keys


class TestDocstringConvention:
    """Test docstring convention compliance for all services."""

    def setup_method(self):
        """Set up docstring parser for tests."""
        self.parser = DocstringParser()

    def test_hello_service_docstring(self):
        """Test hello service docstring follows convention."""
        docstring = BasicServicePlugin.hello.__doc__
        assert docstring is not None
        
        parsed = self.parser.parse(docstring)
        assert parsed.is_valid()
        
        # Verify required sections
        assert parsed.summary == "Hello service."
        assert len(parsed.examples) >= 1
        assert parsed.ai_metadata is not None
        assert parsed.ai_metadata["complexity"] == "low"

    def test_echo_service_docstring(self):
        """Test echo service docstring follows convention."""
        docstring = BasicServicePlugin.echo.__doc__
        assert docstring is not None
        
        parsed = self.parser.parse(docstring)
        assert parsed.is_valid()
        
        # Verify sections
        assert parsed.summary == "Echo service."
        assert "message" in parsed.args
        assert "ValueError" in parsed.raises
        assert len(parsed.examples) >= 1

    def test_get_info_service_docstring(self):
        """Test get_info service docstring follows convention."""
        docstring = BasicServicePlugin.get_info.__doc__
        assert docstring is not None
        
        parsed = self.parser.parse(docstring)
        assert parsed.is_valid()
        
        # Verify AI metadata
        assert parsed.ai_metadata["idempotent"] is True
        assert parsed.ai_metadata["async_safe"] is True
        assert parsed.ai_metadata["side_effects"] == "none"

    def test_all_services_have_valid_docstrings(self):
        """Test that all services have valid docstrings."""
        plugin_instance = BasicServicePlugin()
        
        # Get all service methods
        service_methods = []
        for attr_name in dir(plugin_instance):
            if not attr_name.startswith("_"):
                attr = getattr(plugin_instance, attr_name)
                if hasattr(attr, "_plugginger_service"):
                    service_methods.append((attr_name, attr))
        
        assert len(service_methods) >= 3  # hello, echo, get_info
        
        for method_name, method in service_methods:
            docstring = method.__doc__
            assert docstring is not None, f"Service {method_name} missing docstring"
            
            parsed = self.parser.parse(docstring)
            errors = parsed.validate()
            assert len(errors) == 0, f"Service {method_name} docstring errors: {errors}"


class TestPluginIntegration:
    """Test plugin integration and lifecycle."""

    @pytest.mark.asyncio
    async def test_plugin_loads_successfully(self):
        """Test plugin loads without errors."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicServicePlugin)
        app = builder.build()
        
        # Verify plugin is loaded
        assert app is not None

    @pytest.mark.asyncio
    async def test_all_services_callable(self):
        """Test all services are callable through the app."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicServicePlugin)
        app = builder.build()
        
        # Test hello service
        result1 = await app.call_service("basic_service.hello")
        assert isinstance(result1, str)
        
        # Test echo service
        result2 = await app.call_service("basic_service.echo", message="test")
        assert isinstance(result2, str)
        
        # Test get_info service
        result3 = await app.call_service("basic_service.get_info")
        assert isinstance(result3, dict)

    def test_plugin_metadata(self):
        """Test plugin metadata is correctly defined."""
        # Access plugin metadata
        plugin_meta = getattr(BasicServicePlugin, "_plugginger_plugin", None)
        assert plugin_meta is not None
        
        assert plugin_meta["name"] == "basic_service"
        assert plugin_meta["version"] == "1.0.0"


class TestErrorHandling:
    """Test error handling patterns."""

    @pytest.mark.asyncio
    async def test_service_error_propagation(self):
        """Test that service errors are properly propagated."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicServicePlugin)
        app = builder.build()
        
        # Test that ValueError is raised and propagated
        with pytest.raises(ValueError):
            await app.call_service("basic_service.echo", message="")

    @pytest.mark.asyncio
    async def test_invalid_service_call(self):
        """Test calling non-existent service raises appropriate error."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicServicePlugin)
        app = builder.build()
        
        # This should raise an error for non-existent service
        with pytest.raises(Exception):  # Specific exception type depends on implementation
            await app.call_service("basic_service.non_existent_service")
