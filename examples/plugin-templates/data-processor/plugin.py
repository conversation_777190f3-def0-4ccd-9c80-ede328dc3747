"""
Data Processor Plugin Template

This template demonstrates a data processing plugin with proper docstring convention.
Includes complex data structures, validation, and error handling patterns.
"""

from typing import Any
from dataclasses import dataclass
from plugginger.api import plugin, service, PluginBase
from plugginger.core.exceptions import ValidationError


@dataclass
class ProcessOptions:
    """Configuration options for data processing."""
    algorithm: str = "standard"
    validate_input: bool = True
    include_metadata: bool = True
    max_items: int = 1000


@dataclass
class ProcessResult:
    """Result of data processing operation."""
    processed_data: dict[str, Any]
    metadata: dict[str, Any]
    status: str
    items_processed: int


@plugin(name="data_processor", version="1.0.0")
class DataProcessorPlugin(PluginBase):
    """Data processor plugin demonstrating complex service patterns."""

    @service()
    async def process_data(self, input_data: dict[str, Any], options: ProcessOptions | None = None) -> ProcessResult:
        """Process Data service

        Transforms input data using configurable processing algorithms
        and returns structured results with metadata.
        
        Args:
            input_data: Raw data dictionary with 'content' key required
            options: Processing configuration options (optional)
            
        Returns:
            ProcessResult containing:
                - processed_data: Transformed data structure
                - metadata: Processing statistics and timing
                - status: Success/error status indicator
                - items_processed: Number of items processed
                
        Raises:
            ValidationError: When input_data format is invalid
            ValueError: When processing options are invalid
            
        Example:
            >>> result = await plugin.process_data(
            ...     {"content": "Hello World", "format": "text"},
            ...     ProcessOptions(algorithm="standard")
            ... )
            >>> print(result.processed_data)
            {"content": "Hello World", "format": "text", "processed": True}
            
        AI_METADATA:
            complexity: medium
            dependencies: ["validator", "processor"]
            side_effects: none
            idempotent: true
            async_safe: true
            rate_limit: 100/minute
            timeout: 30
        """
        if options is None:
            options = ProcessOptions()
        
        # Validate input data
        if options.validate_input:
            if not isinstance(input_data, dict):
                raise ValidationError("input_data must be a dictionary")
            if "content" not in input_data:
                raise ValidationError("input_data must contain 'content' key")
        
        # Process data based on algorithm
        processed_data = dict(input_data)
        processed_data["processed"] = True
        processed_data["algorithm"] = options.algorithm
        
        if options.algorithm == "enhanced":
            processed_data["enhanced_features"] = ["normalized", "validated", "enriched"]
        
        # Generate metadata
        metadata = {
            "processing_time": 0.1,
            "algorithm": options.algorithm,
            "validation_enabled": options.validate_input
        }
        
        if options.include_metadata:
            processed_data["_metadata"] = metadata
        
        return ProcessResult(
            processed_data=processed_data,
            metadata=metadata,
            status="success",
            items_processed=1
        )

    @service()
    async def validate_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Validate Data service

        Validates input data against predefined schemas and rules.
        Returns validation results with detailed error information.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            dict[str, Any] containing:
                - valid: Whether data passed validation
                - errors: List of validation errors
                - warnings: List of validation warnings
                - schema_version: Version of validation schema used
                
        Example:
            >>> result = await plugin.validate_data({"content": "test"})
            >>> print(result["valid"])
            True
            
        AI_METADATA:
            complexity: low
            dependencies: []
            side_effects: none
            idempotent: true
            async_safe: true
        """
        errors = []
        warnings = []
        
        # Basic validation rules
        if not isinstance(data, dict):
            errors.append("Data must be a dictionary")
        elif not data:
            errors.append("Data cannot be empty")
        else:
            if "content" not in data:
                warnings.append("Missing 'content' field")
            
            # Check for common issues
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 10000:
                    warnings.append(f"Field '{key}' is very long ({len(value)} chars)")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "schema_version": "1.0.0"
        }

    @service()
    async def get_processing_stats(self) -> dict[str, Any]:
        """Get Processing Stats service

        Returns statistics about data processing operations
        including performance metrics and usage patterns.
        
        Returns:
            dict[str, Any] containing:
                - total_processed: Total number of items processed
                - average_time: Average processing time per item
                - algorithms_used: List of algorithms used
                - error_rate: Percentage of failed operations
                
        Example:
            >>> result = await plugin.get_processing_stats()
            >>> print(result["total_processed"])
            42
            
        AI_METADATA:
            complexity: low
            dependencies: []
            side_effects: read
            idempotent: true
            async_safe: true
        """
        # In a real implementation, this would read from persistent storage
        return {
            "total_processed": 42,
            "average_time": 0.15,
            "algorithms_used": ["standard", "enhanced"],
            "error_rate": 0.02,
            "uptime": "99.9%"
        }
