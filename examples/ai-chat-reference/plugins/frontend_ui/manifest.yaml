name: frontend_ui
version: 1.0.0
description: Frontend UI Plugin for serving static files and HTML templates
author: Plugginger Framework
license: MIT

plugin_type: service
execution_mode: thread

dependencies: []

services:
  - name: get_chat_html
    description: Get the main chat interface HTML
    returns: str
  - name: get_static_file_content
    description: Get static file content by filename
    parameters:
      - name: filename
        type: str
        description: Name of the static file to retrieve
    returns: str | None

events_emitted: []
events_listened: []

configuration:
  schema: {}
  defaults: {}

metadata:
  category: ui
  tags: [frontend, ui, html, static]
  documentation_url: ""
  repository_url: ""
