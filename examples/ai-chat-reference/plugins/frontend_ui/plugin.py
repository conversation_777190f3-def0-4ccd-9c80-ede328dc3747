"""Frontend UI Plugin - Serves static files and HTML templates for the web interface."""

from __future__ import annotations

from typing import Any

from pydantic import BaseModel

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


@plugin(name="frontend_ui", version="1.0.0")
class FrontendUIPlugin(PluginBase):
    """
    Frontend UI Plugin for serving static files and HTML templates.

    This plugin provides the web interface for the AI chat application.
    It serves HTML, CSS, and JavaScript files to create a user-friendly
    chat interface that interacts with the REST API.
    """

    needs: list[Any] = []  # No dependencies - serves static content

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        """Initialize the frontend UI plugin."""
        super().__init__(app, **injected_dependencies)

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the frontend UI plugin."""
        self.app.logger.info("Frontend UI plugin initialized")

    @service()
    async def get_chat_html(self) -> str:
        """
        Get the main chat interface HTML.

        Returns:
            HTML content for the chat interface
        """
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat - Plugginger Reference App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #5a6fd8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 AI Chat Assistant</h1>
            <p>Powered by Plugginger Framework</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    👋 Hello! I'm your AI assistant. How can I help you today?
                </div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            🤔 AI is thinking...
        </div>
        
        <div class="error" id="error"></div>
        
        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="Type your message here..." 
                    required
                >
                <button type="submit" class="send-button" id="sendButton">
                    Send
                </button>
            </form>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.conversationId = null;
                this.initializeElements();
                this.attachEventListeners();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatForm = document.getElementById('chatForm');
                this.loading = document.getElementById('loading');
                this.error = document.getElementById('error');
            }

            attachEventListeners() {
                this.chatForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.sendMessage();
                });

                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                this.addMessage(message, 'user');
                this.messageInput.value = '';
                this.setLoading(true);
                this.hideError();

                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: this.conversationId
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    
                    // Update conversation ID
                    this.conversationId = data.conversation_id;
                    
                    // Add AI response to chat
                    this.addMessage(data.response, 'assistant');
                    
                } catch (error) {
                    console.error('Chat error:', error);
                    this.showError(`Failed to send message: ${error.message}`);
                } finally {
                    this.setLoading(false);
                }
            }

            addMessage(content, role) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                
                // Scroll to bottom
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            setLoading(isLoading) {
                this.loading.style.display = isLoading ? 'block' : 'none';
                this.sendButton.disabled = isLoading;
                this.messageInput.disabled = isLoading;
            }

            showError(message) {
                this.error.textContent = message;
                this.error.style.display = 'block';
            }

            hideError() {
                this.error.style.display = 'none';
            }
        }

        // Initialize the chat app when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>"""

    @service()
    async def get_static_file_content(self, filename: str) -> str | None:
        """
        Get static file content by filename.

        Args:
            filename: Name of the static file to retrieve

        Returns:
            File content as string, or None if not found
        """
        # For now, we only serve the main chat HTML
        if filename in ["", "index.html", "chat.html"]:
            return await self.get_chat_html()
        return None
