"""Tests for Plugginger docstring convention system."""

import pytest
from typing import Any, Dict

from plugginger.core.docstring_convention import (
    DocstringSectionType,
    DocstringSection,
    ServiceDocstring,
    DocstringParser,
    DocstringGenerator,
)
from plugginger.core.exceptions import ConfigurationError


class TestDocstringSection:
    """Test DocstringSection dataclass."""
    
    def test_valid_section_creation(self) -> None:
        """Test creating a valid docstring section."""
        section = DocstringSection(
            section_type=DocstringSectionType.SUMMARY,
            content="Test summary",
            line_number=1
        )
        
        assert section.section_type == DocstringSectionType.SUMMARY
        assert section.content == "Test summary"
        assert section.line_number == 1
    
    def test_empty_content_raises_error(self) -> None:
        """Test that empty content raises ConfigurationError."""
        with pytest.raises(ConfigurationError, match="cannot be empty"):
            DocstringSection(
                section_type=DocstringSectionType.SUMMARY,
                content="   ",  # Only whitespace
                line_number=1
            )


class TestServiceDocstring:
    """Test ServiceDocstring dataclass."""
    
    def test_minimal_valid_docstring(self) -> None:
        """Test creating a minimal valid docstring."""
        docstring = ServiceDocstring(
            summary="Test service",
            examples=[">>> result = test()"]
        )
        
        assert docstring.summary == "Test service"
        assert docstring.examples == [">>> result = test()"]
        assert docstring.is_valid()
    
    def test_validation_missing_summary(self) -> None:
        """Test validation fails when summary is missing."""
        docstring = ServiceDocstring(summary="")
        errors = docstring.validate()
        
        assert "Summary is required" in errors
        assert not docstring.is_valid()
    
    def test_validation_summary_too_long(self) -> None:
        """Test validation fails when summary is too long."""
        long_summary = "x" * 81  # 81 characters
        docstring = ServiceDocstring(summary=long_summary)
        errors = docstring.validate()
        
        assert any("Summary too long" in error for error in errors)
        assert not docstring.is_valid()
    
    def test_validation_summary_with_period(self) -> None:
        """Test validation fails when summary ends with period."""
        docstring = ServiceDocstring(summary="Test service.")
        errors = docstring.validate()
        
        assert "Summary should not end with a period" in errors
        assert not docstring.is_valid()
    
    def test_validation_missing_examples(self) -> None:
        """Test validation fails when examples are missing."""
        docstring = ServiceDocstring(summary="Test service")
        errors = docstring.validate()
        
        assert "At least one example is required" in errors
        assert not docstring.is_valid()
    
    def test_validation_empty_arg_description(self) -> None:
        """Test validation fails for empty argument descriptions."""
        docstring = ServiceDocstring(
            summary="Test service",
            args={"param": ""},
            examples=[">>> test()"]
        )
        errors = docstring.validate()
        
        assert "Argument 'param' description is empty" in errors
        assert not docstring.is_valid()
    
    def test_validation_empty_raises_description(self) -> None:
        """Test validation fails for empty exception descriptions."""
        docstring = ServiceDocstring(
            summary="Test service",
            raises={"ValueError": ""},
            examples=[">>> test()"]
        )
        errors = docstring.validate()
        
        assert "Exception 'ValueError' description is empty" in errors
        assert not docstring.is_valid()
    
    def test_complete_valid_docstring(self) -> None:
        """Test a complete, valid docstring."""
        docstring = ServiceDocstring(
            summary="Process data with advanced algorithms",
            description="This service processes input data using machine learning algorithms.",
            args={
                "data": "Input data to process",
                "options": "Processing options"
            },
            returns="Processed data with metadata",
            raises={
                "ValueError": "When input data is invalid",
                "ProcessingError": "When processing fails"
            },
            examples=[
                ">>> result = await service.process_data({'key': 'value'})",
                ">>> print(result.status)"
            ],
            notes=["Requires GPU for optimal performance"],
            since="1.0.0",
            see_also=["validate_data()", "format_output()"],
            ai_metadata={
                "complexity": "medium",
                "dependencies": ["numpy", "sklearn"],
                "async_safe": True
            }
        )
        
        assert docstring.is_valid()
        assert len(docstring.validate()) == 0


class TestDocstringParser:
    """Test DocstringParser class."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.parser = DocstringParser()
    
    def test_parse_empty_docstring_raises_error(self) -> None:
        """Test that empty docstring raises ConfigurationError."""
        with pytest.raises(ConfigurationError, match="cannot be empty"):
            self.parser.parse("")
    
    def test_parse_minimal_docstring(self) -> None:
        """Test parsing a minimal valid docstring."""
        docstring = '''"""Process data efficiently.
        
        Example:
            >>> result = process_data()
        """'''
        
        result = self.parser.parse(docstring)
        
        assert result.summary == "Process data efficiently."
        assert len(result.examples) == 1
        assert ">>> result = process_data()" in result.examples[0]
    
    def test_parse_complete_docstring(self) -> None:
        """Test parsing a complete docstring with all sections."""
        docstring = '''"""Process input data with configurable options.
        
        This service transforms input data according to the specified processing
        options and returns a structured result with metadata.
        
        Args:
            input_data: Raw data to process. Must contain 'content' key.
            options: Processing configuration. Defaults to standard options.
            
        Returns:
            ProcessResult containing processed data and metadata.
            
        Raises:
            ValidationError: When input_data is malformed
            ProcessingError: When processing fails
            
        Example:
            >>> result = await plugin.process_data(
            ...     {"content": "Hello World"},
            ...     ProcessOptions(format="json")
            ... )
            >>> print(result.processed_data)
            
        Note:
            This service requires the 'data_validator' dependency.
            Processing time scales linearly with input size.
            
        Since:
            1.0.0
            
        See Also:
            - validate_data(): Input validation service
            - format_output(): Output formatting service
            
        AI_METADATA:
            complexity: medium
            dependencies: ["validator", "formatter"]
            side_effects: none
            idempotent: true
            async_safe: true
            rate_limit: 100/minute
        """'''
        
        result = self.parser.parse(docstring)
        
        # Test all sections
        assert result.summary == "Process input data with configurable options."
        assert "transforms input data" in result.description
        assert "input_data" in result.args
        assert "Raw data to process" in result.args["input_data"]
        assert "options" in result.args
        assert "ProcessResult containing" in result.returns
        assert "ValidationError" in result.raises
        assert "ProcessingError" in result.raises
        assert len(result.examples) > 0
        assert len(result.notes) == 1  # Two lines combined into one note
        assert result.since == "1.0.0"
        assert len(result.see_also) == 2
        assert result.ai_metadata["complexity"] == "medium"
        assert result.ai_metadata["idempotent"] is True
        assert isinstance(result.ai_metadata["dependencies"], list)
    
    def test_parse_args_section(self) -> None:
        """Test parsing Args section specifically."""
        content = """param1: First parameter description
        param2: Second parameter with more details"""
        
        args = self.parser._parse_args_section(content)
        
        assert "param1" in args
        assert "param2" in args
        assert args["param1"] == "First parameter description"
        assert args["param2"] == "Second parameter with more details"
    
    def test_parse_raises_section(self) -> None:
        """Test parsing Raises section specifically."""
        content = """ValueError: When value is invalid
        TypeError: When type is wrong"""
        
        raises = self.parser._parse_raises_section(content)
        
        assert "ValueError" in raises
        assert "TypeError" in raises
        assert raises["ValueError"] == "When value is invalid"
        assert raises["TypeError"] == "When type is wrong"
    
    def test_parse_ai_metadata_section(self) -> None:
        """Test parsing AI_METADATA section specifically."""
        content = """complexity: high
        dependencies: ["numpy", "pandas"]
        async_safe: true
        rate_limit: 50/minute
        timeout: 30"""
        
        metadata = self.parser._parse_ai_metadata_section(content)
        
        assert metadata["complexity"] == "high"
        assert metadata["dependencies"] == ["numpy", "pandas"]
        assert metadata["async_safe"] is True
        assert metadata["rate_limit"] == "50/minute"
        assert metadata["timeout"] == 30
    
    def test_clean_docstring(self) -> None:
        """Test docstring cleaning functionality."""
        raw_docstring = '''"""
        Summary line here.
        
        Description with
        multiple lines.
        """'''
        
        cleaned = self.parser._clean_docstring(raw_docstring)
        
        assert cleaned.startswith("Summary line here.")
        assert "Description with" in cleaned
        assert not cleaned.startswith('"""')
        assert not cleaned.endswith('"""')


class TestDocstringGenerator:
    """Test DocstringGenerator class."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.generator = DocstringGenerator()
    
    def test_generate_minimal_template(self) -> None:
        """Test generating a minimal docstring template."""
        template = self.generator.generate_template("test_service")
        
        assert "Test Service service." in template
        assert "Example:" in template
        assert ">>> result = await plugin.test_service()" in template
        assert "AI_METADATA:" in template
    
    def test_generate_template_with_args(self) -> None:
        """Test generating template with arguments."""
        args = {"data": "dict[str, Any]", "options": "Optional[ProcessOptions]"}
        template = self.generator.generate_template("process_data", args=args)
        
        assert "Args:" in template
        assert "data: dict[str, Any]" in template
        assert "options: Optional[ProcessOptions]" in template
    
    def test_generate_template_with_returns(self) -> None:
        """Test generating template with return type."""
        template = self.generator.generate_template(
            "get_result",
            returns="ProcessResult"
        )
        
        assert "Returns:" in template
        assert "ProcessResult" in template
    
    def test_generate_template_with_raises(self) -> None:
        """Test generating template with exceptions."""
        raises = {
            "ValueError": "When input is invalid",
            "ProcessingError": "When processing fails"
        }
        template = self.generator.generate_template("validate", raises=raises)
        
        assert "Raises:" in template
        assert "ValueError: When input is invalid" in template
        assert "ProcessingError: When processing fails" in template
    
    def test_generate_template_without_ai_metadata(self) -> None:
        """Test generating template without AI metadata."""
        template = self.generator.generate_template(
            "simple_service",
            include_ai_metadata=False
        )
        
        assert "AI_METADATA:" not in template
        assert "complexity:" not in template
    
    def test_generate_complete_template(self) -> None:
        """Test generating a complete template with all sections."""
        args = {"input_data": "dict[str, Any]"}
        returns = "ProcessResult"
        raises = {"ValidationError": "When validation fails"}
        
        template = self.generator.generate_template(
            "complex_service",
            args=args,
            returns=returns,
            raises=raises,
            include_ai_metadata=True
        )
        
        # Verify all sections are present
        assert "Complex Service service." in template
        assert "Args:" in template
        assert "Returns:" in template
        assert "Raises:" in template
        assert "Example:" in template
        assert "AI_METADATA:" in template
        
        # Verify specific content
        assert "input_data: dict[str, Any]" in template
        assert "ProcessResult" in template
        assert "ValidationError: When validation fails" in template


class TestDocstringSectionType:
    """Test DocstringSectionType enum."""
    
    def test_all_section_types_defined(self) -> None:
        """Test that all expected section types are defined."""
        expected_types = {
            "summary", "description", "args", "returns", "raises", "example",
            "note", "since", "see_also", "deprecated", "ai_metadata",
            "usage_patterns", "error_handling"
        }
        
        actual_types = {section_type.value for section_type in DocstringSectionType}
        
        assert actual_types == expected_types
    
    def test_section_type_values(self) -> None:
        """Test specific section type values."""
        assert DocstringSectionType.SUMMARY.value == "summary"
        assert DocstringSectionType.AI_METADATA.value == "ai_metadata"
        assert DocstringSectionType.USAGE_PATTERNS.value == "usage_patterns"
