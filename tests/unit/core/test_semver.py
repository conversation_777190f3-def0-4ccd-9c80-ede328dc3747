# tests/unit/core/test_semver.py

"""
Tests for semantic versioning utilities.
"""

import pytest

from plugginger.core.exceptions import Configuration<PERSON>rror, PluginRegistrationError
from plugginger.core.semver import (
    SemVerConstraint,
    VersionConflictResolver,
    normalize_version_constraint,
    validate_constraint_string,
    validate_version_string,
)


class TestSemVerConstraint:
    """Test cases for SemVerConstraint class."""
    
    def test_standard_pep440_constraints(self) -> None:
        """Test standard PEP 440 version constraints."""
        constraint = SemVerConstraint(">=1.0.0,<2.0.0")
        
        assert constraint.satisfies("1.0.0")
        assert constraint.satisfies("1.5.0")
        assert constraint.satisfies("1.9.9")
        assert not constraint.satisfies("0.9.9")
        assert not constraint.satisfies("2.0.0")
    
    def test_caret_operator(self) -> None:
        """Test npm-style caret operator (^)."""
        constraint = SemVerConstraint("^1.2.0")
        
        # Should allow compatible changes within major version
        assert constraint.satisfies("1.2.0")
        assert constraint.satisfies("1.2.1")
        assert constraint.satisfies("1.5.0")
        assert constraint.satisfies("1.9.9")
        
        # Should not allow major version changes
        assert not constraint.satisfies("0.9.9")
        assert not constraint.satisfies("2.0.0")
    
    def test_tilde_operator_patch(self) -> None:
        """Test npm-style tilde operator (~) for patch-level changes."""
        constraint = SemVerConstraint("~1.2.0")
        
        # Should allow patch-level changes
        assert constraint.satisfies("1.2.0")
        assert constraint.satisfies("1.2.1")
        assert constraint.satisfies("1.2.9")
        
        # Should not allow minor version changes
        assert not constraint.satisfies("1.1.9")
        assert not constraint.satisfies("1.3.0")
        assert not constraint.satisfies("2.0.0")
    
    def test_tilde_operator_minor(self) -> None:
        """Test npm-style tilde operator (~) for minor version."""
        constraint = SemVerConstraint("~1.2")
        
        # Should allow patch-level changes within minor version
        assert constraint.satisfies("1.2.0")
        assert constraint.satisfies("1.2.1")
        assert constraint.satisfies("1.2.9")
        
        # Should not allow minor version changes
        assert not constraint.satisfies("1.1.9")
        assert not constraint.satisfies("1.3.0")
    
    def test_exact_version(self) -> None:
        """Test exact version matching."""
        constraint = SemVerConstraint("==1.2.3")
        
        assert constraint.satisfies("1.2.3")
        assert not constraint.satisfies("1.2.2")
        assert not constraint.satisfies("1.2.4")
    
    def test_prerelease_versions(self) -> None:
        """Test handling of pre-release versions."""
        constraint = SemVerConstraint(">=1.0.0a1")
        
        assert constraint.satisfies("1.0.0a1")
        assert constraint.satisfies("1.0.0a2")
        assert constraint.satisfies("1.0.0")
        assert not constraint.satisfies("0.9.9")
    
    def test_invalid_constraint(self) -> None:
        """Test handling of invalid constraint strings."""
        with pytest.raises(ConfigurationError):
            SemVerConstraint("invalid-constraint")
        
        with pytest.raises(ConfigurationError):
            SemVerConstraint(">=")
    
    def test_invalid_version_in_satisfies(self) -> None:
        """Test handling of invalid version in satisfies method."""
        constraint = SemVerConstraint(">=1.0.0")
        
        with pytest.raises(PluginRegistrationError):
            constraint.satisfies("invalid-version")
    
    def test_string_representations(self) -> None:
        """Test string representations of constraints."""
        constraint = SemVerConstraint("^1.2.0")
        
        assert str(constraint) == "^1.2.0"
        assert repr(constraint) == "SemVerConstraint('^1.2.0')"


class TestVersionConflictResolver:
    """Test cases for VersionConflictResolver class."""
    
    def test_no_conflicts(self) -> None:
        """Test scenario with no version conflicts."""
        resolver = VersionConflictResolver()
        resolver.add_constraint("logger", "plugin_a", ">=1.0.0,<2.0.0")
        resolver.add_constraint("logger", "plugin_b", ">=1.5.0,<2.0.0")
        
        service_versions = {"logger": "1.8.0"}
        conflicts = resolver.check_conflicts(service_versions)
        
        assert conflicts == []
    
    def test_version_conflicts(self) -> None:
        """Test detection of version conflicts."""
        resolver = VersionConflictResolver()
        resolver.add_constraint("logger", "plugin_a", ">=1.0.0,<2.0.0")
        resolver.add_constraint("logger", "plugin_b", ">=2.0.0,<3.0.0")
        
        service_versions = {"logger": "1.5.0"}
        conflicts = resolver.check_conflicts(service_versions)
        
        assert len(conflicts) == 1
        assert "plugin_b" in conflicts[0]
        assert ">=2.0.0,<3.0.0" in conflicts[0]
    
    def test_find_compatible_version(self) -> None:
        """Test finding compatible versions."""
        resolver = VersionConflictResolver()
        resolver.add_constraint("cache", "plugin_a", ">=1.0.0,<2.0.0")
        resolver.add_constraint("cache", "plugin_b", ">=1.5.0,<3.0.0")
        
        available_versions = ["1.0.0", "1.4.0", "1.8.0", "2.0.0", "3.0.0"]
        compatible = resolver.find_compatible_version("cache", available_versions)
        
        assert compatible == "1.8.0"  # Latest version that satisfies both constraints
    
    def test_no_compatible_version(self) -> None:
        """Test scenario where no compatible version exists."""
        resolver = VersionConflictResolver()
        resolver.add_constraint("database", "plugin_a", ">=1.0.0,<2.0.0")
        resolver.add_constraint("database", "plugin_b", ">=3.0.0,<4.0.0")
        
        available_versions = ["1.5.0", "2.0.0", "2.5.0"]
        compatible = resolver.find_compatible_version("database", available_versions)
        
        assert compatible is None
    
    def test_no_constraints(self) -> None:
        """Test behavior when no constraints exist for a service."""
        resolver = VersionConflictResolver()
        
        available_versions = ["1.0.0", "2.0.0", "3.0.0"]
        compatible = resolver.find_compatible_version("unconstrained", available_versions)
        
        assert compatible == "3.0.0"  # Should return latest version


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    def test_validate_version_string_valid(self) -> None:
        """Test validation of valid version strings."""
        # Should not raise any exceptions
        validate_version_string("1.0.0")
        validate_version_string("2.1.3")
        validate_version_string("1.0.0a1")
        validate_version_string("1.0.0.dev1")
    
    def test_validate_version_string_invalid(self) -> None:
        """Test validation of invalid version strings."""
        with pytest.raises(PluginRegistrationError):
            validate_version_string("invalid")

        with pytest.raises(PluginRegistrationError):
            validate_version_string("1.2.3-invalid-format")
    
    def test_validate_constraint_string_valid(self) -> None:
        """Test validation of valid constraint strings."""
        # Should not raise any exceptions
        validate_constraint_string(">=1.0.0,<2.0.0")
        validate_constraint_string("^1.2.0")
        validate_constraint_string("~1.2.0")
        validate_constraint_string("==1.0.0")
    
    def test_validate_constraint_string_invalid(self) -> None:
        """Test validation of invalid constraint strings."""
        with pytest.raises(ConfigurationError):
            validate_constraint_string("invalid-constraint")
        
        with pytest.raises(ConfigurationError):
            validate_constraint_string(">=")
    
    def test_normalize_version_constraint(self) -> None:
        """Test normalization of version constraints."""
        # Caret operator
        assert normalize_version_constraint("^1.2.0") == ">=1.2.0,<2.0.0"
        
        # Tilde operator (patch)
        assert normalize_version_constraint("~1.2.0") == ">=1.2.0,<1.3.0"
        
        # Tilde operator (minor)
        assert normalize_version_constraint("~1.2") == ">=1.2.0,<1.3.0"
        
        # Standard constraint (should remain unchanged)
        assert normalize_version_constraint(">=1.0.0,<2.0.0") == ">=1.0.0,<2.0.0"


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_complex_constraints(self) -> None:
        """Test complex version constraints."""
        constraint = SemVerConstraint(">=1.0.0,!=1.2.0,<2.0.0")
        
        assert constraint.satisfies("1.0.0")
        assert constraint.satisfies("1.1.0")
        assert not constraint.satisfies("1.2.0")  # Excluded
        assert constraint.satisfies("1.3.0")
        assert not constraint.satisfies("2.0.0")
    
    def test_zero_major_version(self) -> None:
        """Test handling of 0.x.x versions."""
        constraint = SemVerConstraint("^0.1.0")
        
        assert constraint.satisfies("0.1.0")
        assert constraint.satisfies("0.9.9")
        assert not constraint.satisfies("1.0.0")
    
    def test_multiple_constraints_same_service(self) -> None:
        """Test multiple constraints for the same service."""
        resolver = VersionConflictResolver()
        resolver.add_constraint("service", "plugin_a", ">=1.0.0")
        resolver.add_constraint("service", "plugin_b", "<2.0.0")
        resolver.add_constraint("service", "plugin_c", "!=1.5.0")
        
        service_versions = {"service": "1.3.0"}
        conflicts = resolver.check_conflicts(service_versions)
        
        assert conflicts == []  # 1.3.0 satisfies all constraints
        
        service_versions = {"service": "1.5.0"}
        conflicts = resolver.check_conflicts(service_versions)
        
        assert len(conflicts) == 1  # 1.5.0 violates the != constraint
