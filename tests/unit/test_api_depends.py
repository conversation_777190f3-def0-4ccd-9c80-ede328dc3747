# tests/unit/test_api_depends.py

"""
Unit tests for plugginger.api.depends module.

Tests the Depends class and its functionality for dependency injection.
"""

from __future__ import annotations

import pytest

from plugginger.api.depends import Depends, T
from plugginger.core.exceptions import DependencyError


class TestDependsClass:
    """Test the Depends class."""

    def test_basic_initialization(self) -> None:
        """Test basic Depends initialization."""
        depends = Depends("test_plugin")
        assert depends.dependency == "test_plugin"
        assert depends.optional is False
        assert depends.version is None
        assert depends.version_constraint is None  # Backward compatibility

    def test_initialization_with_optional(self) -> None:
        """Test Depends initialization with optional parameter."""
        depends = Depends("test_plugin", optional=True)
        assert depends.dependency == "test_plugin"
        assert depends.optional is True
        assert depends.version is None
        assert depends.version_constraint is None

    def test_initialization_with_version_constraint(self) -> None:
        """Test Depends initialization with version constraint (backward compatibility)."""
        version_constraint = ">=1.0.0,<2.0.0"
        depends = Depends("test_plugin", version_constraint=version_constraint)
        assert depends.dependency == "test_plugin"
        assert depends.optional is False
        assert depends.version == version_constraint
        assert depends.version_constraint == version_constraint

    def test_initialization_with_version(self) -> None:
        """Test Depends initialization with new version parameter."""
        version = ">=1.0.0,<2.0.0"
        depends = Depends("test_plugin", version=version)
        assert depends.dependency == "test_plugin"
        assert depends.optional is False
        assert depends.version == version
        assert depends.version_constraint == version  # Backward compatibility

    def test_initialization_with_all_parameters(self) -> None:
        """Test Depends initialization with all parameters (using version)."""
        version = ">=1.0.0"
        depends = Depends("test_plugin", optional=True, version=version)
        assert depends.dependency == "test_plugin"
        assert depends.optional is True
        assert depends.version == version
        assert depends.version_constraint == version

    def test_version_and_version_constraint_conflict(self) -> None:
        """Test that specifying both version and version_constraint raises error."""
        with pytest.raises(DependencyError) as exc_info:
            Depends("test_plugin", version=">=1.0.0", version_constraint=">=2.0.0")

        assert "Cannot specify both 'version' and 'version_constraint'" in str(exc_info.value)

    def test_plugin_identifier_property(self) -> None:
        """Test the plugin_identifier property."""
        dependency_name = "test_plugin"
        depends = Depends(dependency_name)
        assert depends.plugin_identifier == dependency_name

    def test_plugin_identifier_property_consistency(self) -> None:
        """Test that plugin_identifier is consistent with dependency."""
        dependency_name = "another_plugin"
        depends = Depends(dependency_name)
        assert depends.plugin_identifier == depends.dependency

    def test_repr_method(self) -> None:
        """Test the __repr__ method."""
        depends = Depends("test_plugin")
        expected_repr = "Depends('test_plugin')"
        assert repr(depends) == expected_repr

    def test_repr_method_with_optional(self) -> None:
        """Test the __repr__ method with optional=True."""
        depends = Depends("test_plugin", optional=True)
        expected_repr = "Depends('test_plugin', optional=True)"
        assert repr(depends) == expected_repr

    def test_repr_method_with_version(self) -> None:
        """Test the __repr__ method with version constraint."""
        depends = Depends("test_plugin", version=">=1.0.0")
        expected_repr = "Depends('test_plugin', version='>=1.0.0')"
        assert repr(depends) == expected_repr

    def test_repr_method_with_version_and_optional(self) -> None:
        """Test the __repr__ method with version and optional."""
        depends = Depends("test_plugin", version=">=1.0.0", optional=True)
        expected_repr = "Depends('test_plugin', version='>=1.0.0', optional=True)"
        assert repr(depends) == expected_repr

    def test_dependency_validation_string_required(self) -> None:
        """Test that dependency must be a string."""
        with pytest.raises(DependencyError) as exc_info:
            Depends(123)  # type: ignore[arg-type]

        assert "Dependency must be a string plugin name" in str(exc_info.value)
        assert "got <class 'int'>" in str(exc_info.value)

    def test_dependency_validation_none_not_allowed(self) -> None:
        """Test that dependency cannot be None."""
        with pytest.raises(DependencyError) as exc_info:
            Depends(None)  # type: ignore[arg-type]

        assert "Dependency must be a string plugin name" in str(exc_info.value)

    def test_dependency_validation_empty_string_allowed(self) -> None:
        """Test that empty string is allowed as dependency."""
        # Empty string might be a valid edge case
        depends = Depends("")
        assert depends.dependency == ""
        assert depends.plugin_identifier == ""

    def test_attributes_are_accessible(self) -> None:
        """Test that all attributes are accessible."""
        depends = Depends("test_plugin", optional=True, version_constraint=">=1.0.0")

        # Test that attributes exist and are accessible
        assert hasattr(depends, "dependency")
        assert hasattr(depends, "optional")
        assert hasattr(depends, "version_constraint")
        assert hasattr(depends, "plugin_identifier")

    def test_attributes_are_immutable_intent(self) -> None:
        """Test that attributes can be modified (no immutability enforced)."""
        depends = Depends("test_plugin")

        # The class doesn't enforce immutability, but we can test current behavior
        original_dependency = depends.dependency
        depends.dependency = "modified_plugin"
        assert depends.dependency == "modified_plugin"
        assert depends.dependency != original_dependency

    def test_version_constraint_types(self) -> None:
        """Test different types of version constraints."""
        # None
        depends1 = Depends("plugin1")
        assert depends1.version_constraint is None

        # String constraint
        depends2 = Depends("plugin2", version_constraint=">=1.0.0")
        assert depends2.version_constraint == ">=1.0.0"

        # Complex constraint
        depends3 = Depends("plugin3", version_constraint=">=1.0.0,<2.0.0,!=1.5.0")
        assert depends3.version_constraint == ">=1.0.0,<2.0.0,!=1.5.0"

    def test_optional_parameter_types(self) -> None:
        """Test different values for optional parameter."""
        # Default (False)
        depends1 = Depends("plugin1")
        assert depends1.optional is False

        # Explicitly False
        depends2 = Depends("plugin2", optional=False)
        assert depends2.optional is False

        # True
        depends3 = Depends("plugin3", optional=True)
        assert depends3.optional is True

    def test_keyword_only_parameters(self) -> None:
        """Test that optional and version_constraint are keyword-only."""
        # This should work (keyword arguments)
        depends1 = Depends("plugin", optional=True, version_constraint=">=1.0.0")
        assert depends1.optional is True
        assert depends1.version_constraint == ">=1.0.0"

        # This should also work (partial keyword arguments)
        depends2 = Depends("plugin", optional=True)
        assert depends2.optional is True
        assert depends2.version_constraint is None

    def test_multiple_instances_independence(self) -> None:
        """Test that multiple Depends instances are independent."""
        depends1 = Depends("plugin1", optional=True)
        depends2 = Depends("plugin2", optional=False)

        assert depends1.dependency != depends2.dependency
        assert depends1.optional != depends2.optional

        # Modifying one shouldn't affect the other
        depends1.dependency = "modified"
        assert depends2.dependency == "plugin2"

    def test_equality_not_implemented(self) -> None:
        """Test that equality comparison uses default object identity."""
        depends1 = Depends("plugin")
        depends2 = Depends("plugin")

        # Should be different objects (no custom __eq__ implemented)
        assert depends1 is not depends2
        assert depends1 != depends2

    def test_hash_not_implemented(self) -> None:
        """Test that hash uses default object hash."""
        depends1 = Depends("plugin")
        depends2 = Depends("plugin")

        # Should have different hashes (no custom __hash__ implemented)
        assert hash(depends1) != hash(depends2)

    def test_dependency_name_variations(self) -> None:
        """Test various valid dependency name formats."""
        # Simple name
        depends1 = Depends("plugin")
        assert depends1.dependency == "plugin"

        # Name with underscores
        depends2 = Depends("my_plugin")
        assert depends2.dependency == "my_plugin"

        # Name with hyphens
        depends3 = Depends("my-plugin")
        assert depends3.dependency == "my-plugin"

        # Name with dots
        depends4 = Depends("my.plugin")
        assert depends4.dependency == "my.plugin"

        # Name with numbers
        depends5 = Depends("plugin123")
        assert depends5.dependency == "plugin123"

    def test_version_constraint_variations(self) -> None:
        """Test various version constraint formats."""
        # Simple version
        depends1 = Depends("plugin", version_constraint="1.0.0")
        assert depends1.version_constraint == "1.0.0"

        # Greater than or equal
        depends2 = Depends("plugin", version_constraint=">=1.0.0")
        assert depends2.version_constraint == ">=1.0.0"

        # Range
        depends3 = Depends("plugin", version_constraint=">=1.0.0,<2.0.0")
        assert depends3.version_constraint == ">=1.0.0,<2.0.0"

        # Complex constraint
        depends4 = Depends("plugin", version_constraint=">=1.0.0,<2.0.0,!=1.5.0")
        assert depends4.version_constraint == ">=1.0.0,<2.0.0,!=1.5.0"


class TestSemVerIntegration:
    """Test SemVer-specific functionality in Depends class."""

    def test_semver_caret_operator(self) -> None:
        """Test npm-style caret operator in version constraints."""
        depends = Depends("plugin", version="^1.2.0")
        assert depends.version == "^1.2.0"
        assert depends.version_constraint == "^1.2.0"

    def test_semver_tilde_operator(self) -> None:
        """Test npm-style tilde operator in version constraints."""
        depends = Depends("plugin", version="~1.2.0")
        assert depends.version == "~1.2.0"
        assert depends.version_constraint == "~1.2.0"

    def test_pep440_range_constraints(self) -> None:
        """Test PEP 440 range constraints."""
        depends = Depends("plugin", version=">=1.0.0,<2.0.0")
        assert depends.version == ">=1.0.0,<2.0.0"

    def test_exact_version_constraint(self) -> None:
        """Test exact version matching."""
        depends = Depends("plugin", version="==1.2.3")
        assert depends.version == "==1.2.3"

    def test_prerelease_version_constraint(self) -> None:
        """Test pre-release version constraints."""
        depends = Depends("plugin", version=">=1.0.0a1")
        assert depends.version == ">=1.0.0a1"

    def test_complex_version_constraint(self) -> None:
        """Test complex version constraints with exclusions."""
        depends = Depends("plugin", version=">=1.0.0,<2.0.0,!=1.5.0")
        assert depends.version == ">=1.0.0,<2.0.0,!=1.5.0"


class TestTypeVariable:
    """Test the T TypeVar."""

    def test_type_variable_exists(self) -> None:
        """Test that T TypeVar is defined."""
        assert T is not None

    def test_type_variable_name(self) -> None:
        """Test that T TypeVar has correct name."""
        assert T.__name__ == "T"



