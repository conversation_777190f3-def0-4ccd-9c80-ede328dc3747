"""Tests for enhanced CLI template generation with docstring convention."""

import pytest
import tempfile
from pathlib import Path

from plugginger.cli.cmd_new import cmd_new_plugin
from plugginger.core.docstring_convention import DocstringParser


class TestCmdNewPluginEnhanced:
    """Test enhanced cmd_new_plugin function with docstring convention."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.output_dir = Path(self.temp_dir)
        self.parser = DocstringParser()

    def test_create_basic_plugin_with_docstring_convention(self) -> None:
        """Test creating basic plugin with proper docstring convention."""
        plugin_name = "test-basic-plugin"
        cmd_new_plugin(plugin_name, self.output_dir)

        plugin_path = self.output_dir / plugin_name
        assert plugin_path.exists()

        # Check plugin file
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        assert plugin_file.exists()

        content = plugin_file.read_text()
        assert "from plugginger.api import plugin, service, PluginBase" in content
        assert "@plugin(" in content
        assert "@service()" in content
        assert "Hello service." in content
        assert "AI_METADATA:" in content
        assert "complexity: low" in content

    def test_generated_plugin_has_valid_docstring(self) -> None:
        """Test that generated plugin has valid docstring according to convention."""
        plugin_name = "test-docstring-validation"
        cmd_new_plugin(plugin_name, self.output_dir)

        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"

        content = plugin_file.read_text()

        # Extract the docstring (simple approach)
        docstring_start = content.find('"""')
        if docstring_start != -1:
            docstring_end = content.find('"""', docstring_start + 3)
            if docstring_end != -1:
                docstring = content[docstring_start:docstring_end + 3]

                # Validate the docstring
                parsed = self.parser.parse(docstring)
                errors = parsed.validate()
                assert len(errors) == 0, f"Generated docstring has errors: {errors}"

                # Check specific requirements
                assert parsed.summary is not None
                assert parsed.ai_metadata is not None
                assert "complexity" in parsed.ai_metadata

    def test_generated_plugin_files_structure(self) -> None:
        """Test that generated plugins have correct file structure."""
        plugin_name = "test-structure"
        cmd_new_plugin(plugin_name, self.output_dir)

        plugin_path = self.output_dir / plugin_name

        # Check all expected files exist
        expected_files = [
            "__init__.py",
            "manifest.yaml",
            "pyproject.toml",
            "README.md",
            f"{plugin_name.replace('-', '_')}.py",
            "tests/__init__.py",
            f"tests/test_{plugin_name.replace('-', '_')}.py"
        ]

        for file_path in expected_files:
            full_path = plugin_path / file_path
            assert full_path.exists(), f"Missing file: {file_path}"
