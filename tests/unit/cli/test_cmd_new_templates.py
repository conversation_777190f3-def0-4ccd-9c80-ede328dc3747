"""Tests for enhanced CLI template generation with docstring convention."""

import pytest
import tempfile
from pathlib import Path
from typing import Any

from plugginger.cli.cmd_new import cmd_new_plugin, _generate_service_template
from plugginger.core.docstring_convention import DocstringParser


class TestServiceTemplateGeneration:
    """Test service template generation with docstring convention."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.parser = DocstringParser()

    def test_generate_basic_template(self) -> None:
        """Test generating basic service template."""
        template = _generate_service_template("hello", "basic", "test-plugin")
        
        assert "@service()" in template
        assert "async def hello(self) -> str:" in template
        assert '"""Hello service.' in template
        assert "AI_METADATA:" in template
        assert "complexity: low" in template
        assert "dependencies: []" in template

    def test_generate_data_processor_template(self) -> None:
        """Test generating data processor service template."""
        template = _generate_service_template("process_data", "data_processor", "test-plugin")
        
        assert "@service()" in template
        assert "async def process_data(self" in template
        assert "ProcessResult" in template
        assert "ValidationError" in template
        assert "ProcessingError" in template
        assert "complexity: medium" in template
        assert '"validator", "processor"' in template

    def test_generate_api_client_template(self) -> None:
        """Test generating API client service template."""
        template = _generate_service_template("fetch_data", "api_client", "test-plugin")
        
        assert "@service()" in template
        assert "async def fetch_data(self" in template
        assert "APIResponse" in template
        assert "ConnectionError" in template
        assert "TimeoutError" in template
        assert "complexity: high" in template
        assert "side_effects: network" in template
        assert "rate_limit: 50/minute" in template

    def test_generate_event_handler_template(self) -> None:
        """Test generating event handler service template."""
        template = _generate_service_template("handle_event", "event_handler", "test-plugin")
        
        assert "@service()" in template
        assert "async def handle_event(self" in template
        assert "EventResult" in template
        assert "EventValidationError" in template
        assert "complexity: medium" in template
        assert "side_effects: write" in template
        assert "idempotent: false" in template

    def test_invalid_template_type_raises_error(self) -> None:
        """Test that invalid template type raises ValueError."""
        with pytest.raises(ValueError, match="Unknown template type"):
            _generate_service_template("test", "invalid_type", "test-plugin")

    def test_all_templates_have_valid_docstrings(self) -> None:
        """Test that all generated templates have valid docstrings."""
        template_types = ["basic", "data_processor", "api_client", "event_handler"]
        
        for template_type in template_types:
            template = _generate_service_template("test_service", template_type, "test-plugin")
            
            # Extract docstring from template
            lines = template.split('\n')
            docstring_start = None
            docstring_end = None
            
            for i, line in enumerate(lines):
                if '"""' in line and docstring_start is None:
                    docstring_start = i
                elif '"""' in line and docstring_start is not None:
                    docstring_end = i
                    break
            
            assert docstring_start is not None, f"No docstring found in {template_type} template"
            assert docstring_end is not None, f"Unclosed docstring in {template_type} template"
            
            # Extract and validate docstring
            docstring_lines = lines[docstring_start:docstring_end + 1]
            docstring = '\n'.join(docstring_lines)
            
            # Parse and validate
            parsed = self.parser.parse(docstring)
            errors = parsed.validate()
            assert len(errors) == 0, f"Template {template_type} has docstring errors: {errors}"


class TestCmdNewPluginEnhanced:
    """Test enhanced cmd_new_plugin function with template support."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.output_dir = Path(self.temp_dir)

    def test_create_basic_plugin(self) -> None:
        """Test creating basic plugin with default template."""
        plugin_name = "test-basic-plugin"
        cmd_new_plugin(plugin_name, self.output_dir, "basic")
        
        plugin_path = self.output_dir / plugin_name
        assert plugin_path.exists()
        
        # Check plugin file
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        assert plugin_file.exists()
        
        content = plugin_file.read_text()
        assert "from plugginger.api import plugin, service, PluginBase" in content
        assert "@plugin(" in content
        assert "@service()" in content
        assert '"""Hello service.' in content

    def test_create_data_processor_plugin(self) -> None:
        """Test creating data processor plugin."""
        plugin_name = "test-processor"
        cmd_new_plugin(plugin_name, self.output_dir, "data_processor")
        
        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        
        content = plugin_file.read_text()
        assert "from typing import Any" in content
        assert "from dataclasses import dataclass" in content
        assert "class ProcessOptions:" in content
        assert "class ProcessResult:" in content
        assert "async def process_data(self" in content

    def test_create_api_client_plugin(self) -> None:
        """Test creating API client plugin."""
        plugin_name = "test-client"
        cmd_new_plugin(plugin_name, self.output_dir, "api_client")
        
        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        
        content = plugin_file.read_text()
        assert "class APIResponse:" in content
        assert "async def fetch_data(self" in content
        assert "ConnectionError" in content

    def test_create_event_handler_plugin(self) -> None:
        """Test creating event handler plugin."""
        plugin_name = "test-handler"
        cmd_new_plugin(plugin_name, self.output_dir, "event_handler")
        
        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        
        content = plugin_file.read_text()
        assert "class EventResult:" in content
        assert "async def handle_event(self" in content
        assert "EventValidationError" in content

    def test_generated_plugin_files_structure(self) -> None:
        """Test that generated plugins have correct file structure."""
        plugin_name = "test-structure"
        cmd_new_plugin(plugin_name, self.output_dir, "basic")
        
        plugin_path = self.output_dir / plugin_name
        
        # Check all expected files exist
        expected_files = [
            "__init__.py",
            "manifest.yaml",
            "pyproject.toml",
            "README.md",
            f"{plugin_name.replace('-', '_')}.py",
            "tests/__init__.py",
            f"tests/test_{plugin_name.replace('-', '_')}.py"
        ]
        
        for file_path in expected_files:
            full_path = plugin_path / file_path
            assert full_path.exists(), f"Missing file: {file_path}"

    def test_generated_test_file_has_docstring_tests(self) -> None:
        """Test that generated test files include docstring validation."""
        plugin_name = "test-docstring-validation"
        cmd_new_plugin(plugin_name, self.output_dir, "basic")
        
        plugin_path = self.output_dir / plugin_name
        test_file = plugin_path / f"tests/test_{plugin_name.replace('-', '_')}.py"
        
        content = test_file.read_text()
        assert "from plugginger.core.docstring_convention import DocstringParser" in content
        assert "DocstringParser()" in content
        assert "parsed.is_valid()" in content

    def test_plugin_name_normalization(self) -> None:
        """Test that plugin names are properly normalized."""
        plugin_name = "test-with-dashes"
        cmd_new_plugin(plugin_name, self.output_dir, "basic")
        
        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / "test_with_dashes.py"
        
        assert plugin_file.exists()
        
        content = plugin_file.read_text()
        assert 'name="test_with_dashes"' in content
        assert "class TestWithDashesPlugin" in content


class TestTemplateDocstringValidation:
    """Test that all template-generated docstrings are valid."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.parser = DocstringParser()
        self.temp_dir = tempfile.mkdtemp()
        self.output_dir = Path(self.temp_dir)

    def test_basic_template_docstring_validation(self) -> None:
        """Test basic template generates valid docstrings."""
        self._test_template_docstring_validation("basic")

    def test_data_processor_template_docstring_validation(self) -> None:
        """Test data processor template generates valid docstrings."""
        self._test_template_docstring_validation("data_processor")

    def test_api_client_template_docstring_validation(self) -> None:
        """Test API client template generates valid docstrings."""
        self._test_template_docstring_validation("api_client")

    def test_event_handler_template_docstring_validation(self) -> None:
        """Test event handler template generates valid docstrings."""
        self._test_template_docstring_validation("event_handler")

    def _test_template_docstring_validation(self, template_type: str) -> None:
        """Helper method to test docstring validation for a template type."""
        plugin_name = f"test-{template_type.replace('_', '-')}"
        cmd_new_plugin(plugin_name, self.output_dir, template_type)
        
        plugin_path = self.output_dir / plugin_name
        plugin_file = plugin_path / f"{plugin_name.replace('-', '_')}.py"
        
        # Read and parse the generated plugin file
        content = plugin_file.read_text()
        
        # Extract docstrings using simple parsing
        # (In a real implementation, you might use AST parsing)
        docstring_start = content.find('"""')
        if docstring_start != -1:
            docstring_end = content.find('"""', docstring_start + 3)
            if docstring_end != -1:
                docstring = content[docstring_start:docstring_end + 3]
                
                # Validate the docstring
                parsed = self.parser.parse(docstring)
                errors = parsed.validate()
                assert len(errors) == 0, f"Template {template_type} generated invalid docstring: {errors}"
                
                # Check specific requirements
                assert parsed.summary is not None
                assert len(parsed.examples) > 0
                assert parsed.ai_metadata is not None
                assert "complexity" in parsed.ai_metadata


class TestTemplateIntegration:
    """Test integration of templates with the broader system."""

    def test_template_types_are_consistent(self) -> None:
        """Test that template types are consistent across the system."""
        from plugginger.cli.cmd_new import ServiceTemplateType
        
        # Get all literal values from the type
        template_types = ServiceTemplateType.__args__
        
        # Test that all template types can generate valid templates
        for template_type in template_types:
            try:
                template = _generate_service_template("test", template_type, "test-plugin")
                assert template is not None
                assert len(template) > 0
            except Exception as e:
                pytest.fail(f"Template type {template_type} failed to generate: {e}")

    def test_all_templates_have_ai_metadata(self) -> None:
        """Test that all templates include AI metadata."""
        from plugginger.cli.cmd_new import ServiceTemplateType
        
        template_types = ServiceTemplateType.__args__
        
        for template_type in template_types:
            template = _generate_service_template("test", template_type, "test-plugin")
            assert "AI_METADATA:" in template
            assert "complexity:" in template
            assert "dependencies:" in template
            assert "side_effects:" in template
            assert "idempotent:" in template
            assert "async_safe:" in template
