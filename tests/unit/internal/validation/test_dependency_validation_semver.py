# tests/unit/internal/validation/test_dependency_validation_semver.py

"""
Tests for SemVer-enhanced dependency validation.
"""

import pytest

from plugginger._internal.validation.dependency_validation import DependencyValidator
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, plugin
from plugginger.core.exceptions import (
    ConfigurationError,
    DependencyError,
    DependencyVersionConflictError,
    PluginRegistrationError,
)


# Mock plugin classes for testing
@plugin(name="logger", version="1.5.0")
class LoggerPlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies) -> None:
        super().__init__(**injected_dependencies)


@plugin(name="database", version="2.1.0")
class DatabasePlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies) -> None:
        super().__init__(**injected_dependencies)


@plugin(name="cache", version="1.2.3")
class CachePlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies) -> None:
        super().__init__(**injected_dependencies)


@plugin(name="user_service", version="1.0.0")
class UserServicePlugin(PluginBase):
    needs = [
        Depends("logger", version="^1.0.0"),
        Depends("database", version=">=2.0.0,<3.0.0"),
        Depends("cache", version="~1.2.0"),
    ]

    def __init__(self, **injected_dependencies) -> None:
        super().__init__(**injected_dependencies)


@plugin(name="conflicting_service", version="1.0.0")
class ConflictingServicePlugin(PluginBase):
    needs = [
        Depends("logger", version=">=2.0.0"),  # Conflicts with user_service
        Depends("database", version="^2.0.0"),
    ]

    def __init__(self, **injected_dependencies) -> None:
        super().__init__(**injected_dependencies)


class TestSemVerDependencyValidation:
    """Test SemVer-enhanced dependency validation."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.plugins = {
            "logger": LoggerPlugin,
            "database": DatabasePlugin,
            "cache": CachePlugin,
            "user_service": UserServicePlugin,
            "conflicting_service": ConflictingServicePlugin,
        }

        def version_resolver(plugin_name: str) -> str:
            plugin_class = self.plugins[plugin_name]
            return plugin_class._plugginger_plugin_version

        def class_resolver(plugin_name: str) -> type[PluginBase]:
            return self.plugins[plugin_name]

        self.validator = DependencyValidator(
            version_resolver_func=version_resolver,
            class_resolver_func=class_resolver,
        )

    def test_semver_caret_operator_success(self) -> None:
        """Test successful validation with caret operator."""
        dependency_declarations = {
            "user_service": [Depends("logger", version="^1.0.0")]
        }

        # Should not raise any exceptions
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_semver_tilde_operator_success(self) -> None:
        """Test successful validation with tilde operator."""
        dependency_declarations = {
            "user_service": [Depends("cache", version="~1.2.0")]
        }

        # Should not raise any exceptions
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_pep440_range_constraint_success(self) -> None:
        """Test successful validation with PEP 440 range constraint."""
        dependency_declarations = {
            "user_service": [Depends("database", version=">=2.0.0,<3.0.0")]
        }

        # Should not raise any exceptions
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_semver_caret_operator_failure(self) -> None:
        """Test failed validation with caret operator."""
        dependency_declarations = {
            "user_service": [Depends("database", version="^1.0.0")]  # database is 2.1.0
        }

        with pytest.raises(DependencyVersionConflictError) as exc_info:
            self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

        assert "user_service" in str(exc_info.value)
        assert "database" in str(exc_info.value)
        assert "^1.0.0" in str(exc_info.value)
        assert "2.1.0" in str(exc_info.value)

    def test_semver_tilde_operator_failure(self) -> None:
        """Test failed validation with tilde operator."""
        dependency_declarations = {
            "user_service": [Depends("cache", version="~1.1.0")]  # cache is 1.2.3
        }

        with pytest.raises(DependencyVersionConflictError) as exc_info:
            self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

        assert "user_service" in str(exc_info.value)
        assert "cache" in str(exc_info.value)
        assert "~1.1.0" in str(exc_info.value)
        assert "1.2.3" in str(exc_info.value)

    def test_global_version_conflicts(self) -> None:
        """Test detection of global version conflicts across multiple plugins."""
        dependency_declarations = {
            "user_service": [Depends("logger", version="^1.0.0")],  # logger 1.5.0 satisfies ^1.0.0
            "conflicting_service": [Depends("logger", version=">=2.0.0")],  # but not >=2.0.0
        }

        with pytest.raises(DependencyVersionConflictError) as exc_info:
            self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

        error_message = str(exc_info.value)
        assert "Version conflicts detected" in error_message
        assert "logger" in error_message
        assert "conflicting_service" in error_message
        assert ">=2.0.0" in error_message

    def test_multiple_compatible_constraints(self) -> None:
        """Test multiple compatible constraints for the same service."""
        dependency_declarations = {
            "user_service": [Depends("database", version=">=2.0.0,<3.0.0")],
            "conflicting_service": [Depends("database", version="^2.0.0")],  # Compatible
        }

        # Should not raise any exceptions
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_invalid_version_constraint_format(self) -> None:
        """Test handling of invalid version constraint formats."""
        dependency_declarations = {
            "user_service": [Depends("logger", version="invalid-constraint")]
        }

        with pytest.raises(ConfigurationError) as exc_info:
            self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

        error_message = str(exc_info.value)
        assert "invalid version constraint" in error_message.lower()
        # The error message format may vary, so check for key components
        assert "invalid-constraint" in error_message

    def test_optional_dependency_missing_version(self) -> None:
        """Test handling of optional dependencies with missing versions."""
        # Add a mock plugin that doesn't exist
        dependency_declarations = {
            "user_service": [Depends("nonexistent_plugin", version="^1.0.0", optional=True)]
        }

        # Should not raise exceptions for optional dependencies
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_required_dependency_missing_version(self) -> None:
        """Test handling of required dependencies with missing versions."""
        dependency_declarations = {
            "user_service": [Depends("nonexistent_plugin", version="^1.0.0", optional=False)]
        }

        with pytest.raises(DependencyError) as exc_info:
            self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

        assert "Cannot resolve version" in str(exc_info.value)
        assert "nonexistent_plugin" in str(exc_info.value)

    def test_backward_compatibility_version_constraint(self) -> None:
        """Test backward compatibility with version_constraint parameter."""
        dependency_declarations = {
            "user_service": [Depends("logger", version_constraint="^1.0.0")]
        }

        # Should work with legacy parameter name
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_mixed_version_and_version_constraint_usage(self) -> None:
        """Test mixed usage of new 'version' and legacy 'version_constraint' parameters."""
        dependency_declarations = {
            "user_service": [
                Depends("logger", version="^1.0.0"),  # New parameter
                Depends("database", version_constraint=">=2.0.0,<3.0.0"),  # Legacy parameter
            ]
        }

        # Should work with mixed parameter usage
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_complex_constraint_validation(self) -> None:
        """Test validation of complex version constraints."""
        dependency_declarations = {
            "user_service": [Depends("database", version=">=2.0.0,<3.0.0,!=2.0.5")]
        }

        # Should not raise any exceptions (database is 2.1.0, which satisfies the constraint)
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_prerelease_version_handling(self) -> None:
        """Test handling of pre-release versions."""
        # Mock a plugin with pre-release version
        @plugin(name="beta_plugin", version="2.0.0a1")
        class BetaPlugin(PluginBase):
            needs = []

            def __init__(self, **injected_dependencies) -> None:
                super().__init__(**injected_dependencies)

        self.plugins["beta_plugin"] = BetaPlugin

        dependency_declarations = {
            "user_service": [Depends("beta_plugin", version=">=2.0.0a1")]
        }

        # Should handle pre-release versions correctly
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)

    def test_edge_case_zero_major_version(self) -> None:
        """Test handling of 0.x.x versions with caret operator."""
        # Mock a plugin with 0.x.x version
        @plugin(name="experimental_plugin", version="0.5.2")
        class ExperimentalPlugin(PluginBase):
            needs = []

            def __init__(self, **injected_dependencies) -> None:
                super().__init__(**injected_dependencies)

        self.plugins["experimental_plugin"] = ExperimentalPlugin

        dependency_declarations = {
            "user_service": [Depends("experimental_plugin", version="^0.5.0")]
        }

        # Should handle 0.x.x versions correctly with caret operator
        self.validator.validate_dependency_versions_and_signatures(dependency_declarations)
