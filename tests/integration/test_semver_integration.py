# tests/integration/test_semver_integration.py

"""
End-to-end integration tests for SemVer-aware dependency system.

These tests verify the complete integration of the SemVer system from
API usage through the builder pipeline to runtime dependency injection.
"""

from typing import Any

import pytest

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import DependencyVersionConflictError


# Test plugins with various version constraints
@plugin(name="logger_service", version="1.5.0")
class LoggerServicePlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)

    @service()
    async def log(self, message: str) -> None:
        """Log a message."""
        pass


@plugin(name="database_service", version="2.1.0")
class DatabaseServicePlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)

    @service()
    async def query(self, sql: str) -> list[dict[str, str]]:
        """Execute a database query."""
        return [{"result": "mock"}]


@plugin(name="cache_service", version="1.2.3")
class CacheServicePlugin(PluginBase):
    needs = []

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)

    @service()
    async def get(self, key: str) -> str | None:
        """Get a value from cache."""
        return None

    @service()
    async def set(self, key: str, value: str) -> None:
        """Set a value in cache."""
        pass


@plugin(name="user_service", version="1.0.0")
class UserServicePlugin(PluginBase):
    needs = [
        Depends("logger_service", version="^1.0.0"),  # 1.x.x compatible
        Depends("database_service", version=">=2.0.0,<3.0.0"),  # PEP 440 range
        Depends("cache_service", version="~1.2.0"),  # 1.2.x compatible
    ]

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)

    @service()
    async def create_user(self, username: str) -> dict[str, str]:
        """Create a new user."""
        return {"id": "123", "username": username}


@plugin(name="notification_service", version="1.0.0")
class NotificationServicePlugin(PluginBase):
    needs = [
        Depends("logger_service", version="^1.0.0"),
        Depends("optional_service", version=">=1.0.0", optional=True),
    ]

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)

    @service()
    async def send_notification(self, message: str) -> None:
        """Send a notification."""
        pass


@plugin(name="conflicting_service", version="1.0.0")
class ConflictingServicePlugin(PluginBase):
    needs = [
        Depends("logger_service", version=">=2.0.0"),  # Conflicts with user_service
    ]

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)


@plugin(name="invalid_constraint_service", version="1.0.0")
class InvalidConstraintServicePlugin(PluginBase):
    needs = [
        Depends("logger_service", version="invalid-constraint"),
    ]

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)


class TestSemVerIntegration:
    """Integration tests for SemVer-aware dependency system."""

    def test_successful_build_with_semver_constraints(self) -> None:
        """Test successful app build with various SemVer constraints."""
        builder = PluggingerAppBuilder("test_app")

        # Register plugins with compatible versions
        builder.include(LoggerServicePlugin)
        builder.include(DatabaseServicePlugin)
        builder.include(CacheServicePlugin)
        builder.include(UserServicePlugin)

        # Build should succeed
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

        # Verify services are available
        assert app.has_service("logger_service.log")
        assert app.has_service("database_service.query")
        assert app.has_service("cache_service.get")
        assert app.has_service("user_service.create_user")

    def test_caret_operator_compatibility(self) -> None:
        """Test npm-style caret operator (^) compatibility."""
        builder = PluggingerAppBuilder("test_app")

        # logger_service is 1.5.0, which satisfies ^1.0.0
        builder.include(LoggerServicePlugin)
        builder.include(UserServicePlugin)  # depends on logger ^1.0.0

        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_tilde_operator_compatibility(self) -> None:
        """Test npm-style tilde operator (~) compatibility."""
        builder = PluggingerAppBuilder("test_app")

        # cache_service is 1.2.3, which satisfies ~1.2.0
        builder.include(CacheServicePlugin)
        builder.include(LoggerServicePlugin)
        builder.include(DatabaseServicePlugin)
        builder.include(UserServicePlugin)  # depends on cache ~1.2.0

        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_pep440_range_compatibility(self) -> None:
        """Test PEP 440 range constraint compatibility."""
        builder = PluggingerAppBuilder("test_app")

        # database_service is 2.1.0, which satisfies >=2.0.0,<3.0.0
        builder.include(DatabaseServicePlugin)
        builder.include(LoggerServicePlugin)
        builder.include(CacheServicePlugin)
        builder.include(UserServicePlugin)  # depends on database >=2.0.0,<3.0.0

        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_optional_dependency_handling(self) -> None:
        """Test handling of optional dependencies."""
        builder = PluggingerAppBuilder("test_app")

        # Include notification_service which has optional dependency on non-existent service
        builder.include(LoggerServicePlugin)
        builder.include(NotificationServicePlugin)

        # Build should succeed despite missing optional dependency
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)
        assert app.has_service("notification_service.send_notification")

    def test_version_conflict_detection(self) -> None:
        """Test detection of version conflicts between plugins."""
        builder = PluggingerAppBuilder("test_app")

        # Register plugins with conflicting version requirements
        builder.include(LoggerServicePlugin)  # version 1.5.0
        builder.include(DatabaseServicePlugin)  # version 2.1.0
        builder.include(CacheServicePlugin)  # version 1.2.3
        builder.include(UserServicePlugin)    # requires logger ^1.0.0 (satisfied)
        builder.include(ConflictingServicePlugin)  # requires logger >=2.0.0 (conflict)

        # Build should fail with version conflict error
        with pytest.raises(DependencyVersionConflictError) as exc_info:
            builder.build()

        error_message = str(exc_info.value)
        assert "Version conflicts detected" in error_message
        assert "logger_service" in error_message
        assert "conflicting_service" in error_message

    def test_invalid_version_constraint_error(self) -> None:
        """Test handling of invalid version constraints."""
        builder = PluggingerAppBuilder("test_app")

        builder.include(LoggerServicePlugin)
        builder.include(InvalidConstraintServicePlugin)

        # Build should fail with configuration error
        with pytest.raises(Exception) as exc_info:
            builder.build()

        # Should contain information about the invalid constraint
        error_message = str(exc_info.value)
        assert "invalid" in error_message.lower()

    def test_backward_compatibility_version_constraint(self) -> None:
        """Test backward compatibility with version_constraint parameter."""
        
        @plugin(name="legacy_service", version="1.0.0")
        class LegacyServicePlugin(PluginBase):
            needs = [
                Depends("logger_service", version_constraint="^1.0.0"),  # Legacy parameter
            ]

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        builder = PluggingerAppBuilder("test_app")
        builder.include(LoggerServicePlugin)
        builder.include(LegacyServicePlugin)

        # Build should succeed with legacy parameter
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_mixed_version_parameter_usage(self) -> None:
        """Test mixed usage of 'version' and 'version_constraint' parameters."""
        
        @plugin(name="mixed_service", version="1.0.0")
        class MixedServicePlugin(PluginBase):
            needs = [
                Depends("logger_service", version="^1.0.0"),  # New parameter
                Depends("database_service", version_constraint=">=2.0.0,<3.0.0"),  # Legacy
            ]

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        builder = PluggingerAppBuilder("test_app")
        builder.include(LoggerServicePlugin)
        builder.include(DatabaseServicePlugin)
        builder.include(MixedServicePlugin)

        # Build should succeed with mixed parameter usage
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_complex_dependency_graph_with_semver(self) -> None:
        """Test complex dependency graph with multiple SemVer constraints."""
        builder = PluggingerAppBuilder("test_app")

        # Build a complex dependency graph
        builder.include(LoggerServicePlugin)
        builder.include(DatabaseServicePlugin)
        builder.include(CacheServicePlugin)
        builder.include(UserServicePlugin)
        builder.include(NotificationServicePlugin)

        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

        # Verify all services are properly instantiated and available
        services = [
            "logger_service.log",
            "database_service.query",
            "cache_service.get",
            "cache_service.set",
            "user_service.create_user",
            "notification_service.send_notification",
        ]

        for service_name in services:
            assert app.has_service(service_name), f"Service {service_name} should be available"

    def test_prerelease_version_handling(self) -> None:
        """Test handling of pre-release versions in constraints."""
        
        @plugin(name="beta_service", version="2.0.0a1")
        class BetaServicePlugin(PluginBase):
            needs = []

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        @plugin(name="beta_consumer", version="1.0.0")
        class BetaConsumerPlugin(PluginBase):
            needs = [
                Depends("beta_service", version=">=2.0.0a1"),
            ]

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        builder = PluggingerAppBuilder("test_app")
        builder.include(BetaServicePlugin)
        builder.include(BetaConsumerPlugin)

        # Build should succeed with pre-release version
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)

    def test_zero_major_version_caret_operator(self) -> None:
        """Test caret operator behavior with 0.x.x versions."""
        
        @plugin(name="experimental_service", version="0.5.2")
        class ExperimentalServicePlugin(PluginBase):
            needs = []

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        @plugin(name="experimental_consumer", version="1.0.0")
        class ExperimentalConsumerPlugin(PluginBase):
            needs = [
                Depends("experimental_service", version="^0.5.0"),
            ]

            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)

        builder = PluggingerAppBuilder("test_app")
        builder.include(ExperimentalServicePlugin)
        builder.include(ExperimentalConsumerPlugin)

        # Build should succeed with 0.x.x caret constraint
        app = builder.build()
        assert isinstance(app, PluggingerAppInstance)
