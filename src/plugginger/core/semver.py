# src/plugginger/core/semver.py

"""
Semantic versioning utilities for Plugginger framework.

This module provides utilities for parsing, validating, and resolving
semantic version constraints in plugin dependencies.
"""

from __future__ import annotations

import re

from packaging.specifiers import InvalidSpecifier, SpecifierSet
from packaging.version import InvalidVersion, Version

from plugginger.core.exceptions import ConfigurationError, PluginRegistrationError


class SemVerConstraint:
    """
    Semantic version constraint parser and validator.

    This class provides utilities for parsing and validating semantic version
    constraints used in plugin dependencies. It supports standard PEP 440
    version specifiers as well as npm-style caret (^) and tilde (~) operators.

    Examples:
        ```python
        # Standard PEP 440 specifiers
        constraint = SemVerConstraint(">=1.0.0,<2.0.0")
        assert constraint.satisfies("1.5.0")
        assert not constraint.satisfies("2.0.0")

        # npm-style caret operator (compatible within major version)
        constraint = SemVerConstraint("^1.2.0")
        assert constraint.satisfies("1.5.0")
        assert not constraint.satisfies("2.0.0")

        # npm-style tilde operator (compatible within minor version)
        constraint = SemVerConstraint("~1.2.0")
        assert constraint.satisfies("1.2.5")
        assert not constraint.satisfies("1.3.0")
        ```
    """

    def __init__(self, constraint_str: str) -> None:
        """
        Initialize a semantic version constraint.

        Args:
            constraint_str: Version constraint string (e.g., ">=1.0.0,<2.0.0", "^1.2.0", "~1.2.0")

        Raises:
            ConfigurationError: If the constraint string is invalid
        """
        self.original_constraint = constraint_str
        self.normalized_constraint = self._normalize_constraint(constraint_str)

        try:
            self.specifier_set = SpecifierSet(self.normalized_constraint)
        except InvalidSpecifier as e:
            raise ConfigurationError(
                f"Invalid version constraint '{constraint_str}': {e}"
            ) from e

    def _normalize_constraint(self, constraint_str: str) -> str:
        """
        Normalize npm-style constraints to PEP 440 format.

        Args:
            constraint_str: Original constraint string

        Returns:
            Normalized constraint string compatible with PEP 440

        Raises:
            ConfigurationError: If constraint format is invalid
        """
        constraint_str = constraint_str.strip()

        # Handle caret operator (^1.2.0 -> >=1.2.0,<2.0.0)
        caret_match = re.match(r'^\^(\d+)\.(\d+)\.(\d+)(?:[-+].*)?$', constraint_str)
        if caret_match:
            major, minor, patch = caret_match.groups()[:3]
            next_major = int(major) + 1
            return f">={major}.{minor}.{patch},<{next_major}.0.0"

        # Handle tilde operator (~1.2.0 -> >=1.2.0,<1.3.0)
        tilde_match = re.match(r'^~(\d+)\.(\d+)\.(\d+)(?:[-+].*)?$', constraint_str)
        if tilde_match:
            major, minor, patch = tilde_match.groups()[:3]
            next_minor = int(minor) + 1
            return f">={major}.{minor}.{patch},<{major}.{next_minor}.0"

        # Handle tilde operator for minor version (~1.2 -> >=1.2.0,<1.3.0)
        tilde_minor_match = re.match(r'^~(\d+)\.(\d+)$', constraint_str)
        if tilde_minor_match:
            major, minor = tilde_minor_match.groups()
            next_minor = int(minor) + 1
            return f">={major}.{minor}.0,<{major}.{next_minor}.0"

        # Return as-is for standard PEP 440 constraints
        return constraint_str

    def satisfies(self, version_str: str) -> bool:
        """
        Check if a version satisfies this constraint.

        Args:
            version_str: Version string to check (e.g., "1.5.0")

        Returns:
            True if the version satisfies the constraint, False otherwise

        Raises:
            PluginRegistrationError: If the version string is invalid
        """
        try:
            version = Version(version_str)
        except InvalidVersion as e:
            raise PluginRegistrationError(
                f"Invalid version '{version_str}': {e}"
            ) from e

        return version in self.specifier_set

    def __str__(self) -> str:
        """String representation of the constraint."""
        return self.original_constraint

    def __repr__(self) -> str:
        """Debug representation of the constraint."""
        return f"SemVerConstraint({self.original_constraint!r})"


class VersionConflictResolver:
    """
    Resolver for version conflicts in plugin dependency graphs.

    This class provides utilities for detecting and resolving version conflicts
    when multiple plugins depend on the same service with different version
    constraints.
    """

    def __init__(self) -> None:
        """Initialize the version conflict resolver."""
        self.constraints: dict[str, list[tuple[str, SemVerConstraint]]] = {}

    def add_constraint(self, service_name: str, dependent_plugin: str, constraint_str: str) -> None:
        """
        Add a version constraint for a service.

        Args:
            service_name: Name of the service being constrained
            dependent_plugin: Name of the plugin declaring the constraint
            constraint_str: Version constraint string

        Raises:
            ConfigurationError: If the constraint string is invalid
        """
        constraint = SemVerConstraint(constraint_str)

        if service_name not in self.constraints:
            self.constraints[service_name] = []

        self.constraints[service_name].append((dependent_plugin, constraint))

    def check_conflicts(self, service_versions: dict[str, str]) -> list[str]:
        """
        Check for version conflicts and return a list of conflict descriptions.

        Args:
            service_versions: Mapping of service names to their actual versions

        Returns:
            List of conflict descriptions (empty if no conflicts)
        """
        conflicts = []

        for service_name, constraint_list in self.constraints.items():
            if service_name not in service_versions:
                continue

            actual_version = service_versions[service_name]
            failed_constraints = []

            for dependent_plugin, constraint in constraint_list:
                if not constraint.satisfies(actual_version):
                    failed_constraints.append((dependent_plugin, constraint))

            if failed_constraints:
                conflict_details = []
                for plugin, constraint in failed_constraints:
                    conflict_details.append(f"'{plugin}' requires '{constraint}'")

                conflicts.append(
                    f"Version conflict for service '{service_name}' (actual: {actual_version}): "
                    f"{', '.join(conflict_details)}"
                )

        return conflicts

    def find_compatible_version(self, service_name: str, available_versions: list[str]) -> str | None:
        """
        Find a version that satisfies all constraints for a service.

        Args:
            service_name: Name of the service
            available_versions: List of available versions to choose from

        Returns:
            Compatible version string, or None if no compatible version exists
        """
        if service_name not in self.constraints:
            # No constraints, return the latest version
            return max(available_versions, key=Version) if available_versions else None

        constraint_list = self.constraints[service_name]

        # Find versions that satisfy all constraints
        compatible_versions = []
        for version_str in available_versions:
            if all(constraint.satisfies(version_str) for _, constraint in constraint_list):
                compatible_versions.append(version_str)

        # Return the latest compatible version
        return max(compatible_versions, key=Version) if compatible_versions else None


def validate_version_string(version_str: str) -> None:
    """
    Validate a version string according to PEP 440.

    Args:
        version_str: Version string to validate

    Raises:
        PluginRegistrationError: If the version string is invalid
    """
    try:
        Version(version_str)
    except InvalidVersion as e:
        raise PluginRegistrationError(
            f"Invalid version '{version_str}': {e}"
        ) from e


def validate_constraint_string(constraint_str: str) -> None:
    """
    Validate a version constraint string.

    Args:
        constraint_str: Constraint string to validate

    Raises:
        ConfigurationError: If the constraint string is invalid
    """
    SemVerConstraint(constraint_str)  # Will raise ConfigurationError if invalid


def normalize_version_constraint(constraint_str: str) -> str:
    """
    Normalize a version constraint to PEP 440 format.

    Args:
        constraint_str: Original constraint string

    Returns:
        Normalized constraint string

    Raises:
        ConfigurationError: If the constraint string is invalid
    """
    constraint = SemVerConstraint(constraint_str)
    return constraint.normalized_constraint
