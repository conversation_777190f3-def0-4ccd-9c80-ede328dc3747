# src/plugginger/api/depends.py

"""
Dependency injection utilities using the DI container.

This module provides the Depends class and utilities for dependency injection
in plugin methods, without circular import dependencies.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, TypeVar

from plugginger.core.exceptions import DependencyError

if TYPE_CHECKING:
    pass

T = TypeVar("T")


class Depends:
    """
    Dependency injection marker for plugin dependencies.

    This is a pure data class that marks plugin dependencies. The actual
    dependency resolution is handled by the PluggingerAppBuilder through
    GenericPluginProxy instances.

    Examples:
        ```python
        @plugin(name="user_service", version="1.0.0")
        class UserServicePlugin(PluginBase):
            needs: List[Depends] = [
                Depends("database"),
                Depends("auth", version=">=1.0.0,<2.0.0"),
                Depends("cache", version="~=1.2.0"),  # 1.2.x compatible
                Depends("metrics", version="^2.0.0"),  # 2.x.x compatible
                Depends("optional_service", version=">=1.0.0", optional=True)
            ]
        ```
    """

    def __init__(
        self,
        dependency: str,
        *,
        optional: bool = False,
        version: str | None = None,
        version_constraint: str | None = None,
    ) -> None:
        """
        Initialize a dependency injection marker.

        Args:
            dependency: The plugin name to depend on (e.g., "database", "auth")
            optional: Whether the dependency is optional
            version: Version constraint for plugin dependencies (e.g., ">=1.0.0,<2.0.0")
            version_constraint: Deprecated alias for version parameter (for backward compatibility)

        Note:
            The version_constraint parameter is deprecated. Use version instead.
        """
        if not isinstance(dependency, str):
            raise DependencyError(
                f"Dependency must be a string plugin name, got {type(dependency)}"
            )

        # Handle backward compatibility for version_constraint
        if version_constraint is not None and version is not None:
            raise DependencyError(
                "Cannot specify both 'version' and 'version_constraint'. Use 'version' only."
            )

        # Use version_constraint as fallback for backward compatibility
        final_version = version if version is not None else version_constraint

        self.dependency = dependency
        self.optional = optional
        self.version = final_version
        # Keep version_constraint for backward compatibility
        self.version_constraint = final_version

    @property
    def plugin_identifier(self) -> str:
        """
        Get the plugin identifier for dependency resolution.

        Returns:
            Plugin identifier string
        """
        return self.dependency



    def __repr__(self) -> str:
        """String representation for debugging."""
        parts = [f"{self.dependency!r}"]
        if self.version is not None:
            parts.append(f"version={self.version!r}")
        if self.optional:
            parts.append(f"optional={self.optional}")
        return f"Depends({', '.join(parts)})"
