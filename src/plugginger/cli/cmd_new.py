"""
CLI command for creating new Plugginger projects and plugins.
"""

from pathlib import Path


def cmd_new_plugin(plugin_name: str, output_dir: Path) -> None:
    """
    Creates a new Plugginger plugin project.

    Args:
        plugin_name: The name of the plugin to create.
        output_dir: The directory where the plugin project will be created.
    """
    project_path = output_dir / plugin_name
    project_path.mkdir(parents=True, exist_ok=True)

    # Create __init__.py
    (project_path / "__init__.py").touch()

    # Create manifest.yaml
    manifest_content = f"""
name: {plugin_name.replace('-', '_')} # Use valid Python identifier for plugin name
version: 0.1.0
description: A new Plugginger plugin.
plugins:
  - module: {plugin_name.replace('-', '_')}
    class: {plugin_name.replace('-', '_').title().replace('_', '')}Plugin
"""
    (project_path / "manifest.yaml").write_text(manifest_content.strip())

    # Create pyproject.toml
    pyproject_content = f"""
[tool.poetry]
name = "plugginger-{plugin_name}"
version = "0.1.0"
description = "A new Plugginger plugin."
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
plugginger = ">=0.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
mypy = "^1.15.0"
ruff = "^0.11.11"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = "."

[tool.mypy]
strict = true

[tool.ruff]
line-length = 120
target-version = "py39"
"""
    (project_path / "pyproject.toml").write_text(pyproject_content.strip())

    # Create README.md
    readme_content = f"""
# {plugin_name}

A new Plugginger plugin.
"""
    (project_path / "README.md").write_text(readme_content.strip())

    # Create tests directory and __init__.py
    tests_path = project_path / "tests"
    tests_path.mkdir(exist_ok=True)
    (tests_path / "__init__.py").touch()

    # Create a basic test file
    test_content = f"""
import pytest
from plugginger.api import plugin, service, PluginBase, PluggingerAppBuilder
from plugginger.api.app import PluggingerAppInstance

@plugin(name="{plugin_name.replace('-', '_')}", version="0.1.0")
class {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(PluginBase):
    @service()
    async def hello(self) -> str:
        return "Hello from {plugin_name}!"

@pytest.mark.asyncio
async def test_{plugin_name.replace('-', '_')}_hello_service():
    builder = PluggingerAppBuilder(app_name="test_app")
    builder.include({plugin_name.replace('-', '_').title().replace('_', '')}Plugin)
    app = builder.build()

    # Corrected: Use call_service and await it
    result = await app.call_service("{plugin_name.replace('-', '_')}.hello")
    assert result == "Hello from {plugin_name}!"
"""
    (tests_path / f"test_{plugin_name.replace('-', '_')}.py").write_text(test_content.strip())

    # Create a basic plugin file
    plugin_file_content = f"""
from plugginger.api import plugin, service, PluginBase

@plugin(name="{plugin_name.replace('-', '_')}", version="0.1.0")
class {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(PluginBase):
    @service()
    async def hello(self) -> str:
        return "Hello from {plugin_name}!"
"""
    (project_path / f"{plugin_name.replace('-', '_')}.py").write_text(plugin_file_content.strip())


    print(f"Successfully created plugin '{plugin_name}' at {project_path}")
    print(f"To get started, run: cd {project_path} && poetry install && poetry run pytest")
