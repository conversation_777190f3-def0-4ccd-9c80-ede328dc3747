# src/plugginger/_internal/builder_phases/dependency_orchestrator.py

"""
Dependency graph management for the PluggingerAppBuilder.

This module contains the DependencyOrchestrator class which handles the creation,
validation, and topological sorting of plugin dependency graphs.
"""

from __future__ import annotations

import logging
from collections.abc import Callable
from typing import TYPE_CHECKING, Any

from plugginger._internal.graph import DependencyGraph
from plugginger.api.depends import Depends
from plugginger.core.exceptions import (
    CircularDependencyError,
    DependencyError,
    DependencyVersionConflictError,
    MissingDependencyError,
)

if TYPE_CHECKING:
    from plugginger.api.plugin import PluginBase

# Type aliases for resolver functions
VersionResolverFunc = Callable[[str], str]
ClassResolverFunc = Callable[[str], type[Any]]  # type[PluginBase] in runtime


class DependencyOrchestrator:
    """
    Manages dependency graph construction, validation, and resolution ordering.

    This class handles the complex process of building a dependency graph from
    plugin registrations and their declared dependencies, validating the graph
    for cycles and missing dependencies, and determining the correct instantiation
    order through topological sorting.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the dependency orchestrator.

        Args:
            logger: Optional logger for dependency management messages
        """
        self._logger = logger or logging.getLogger(__name__)

    def build_graph(
        self,
        registered_item_classes: dict[str, type[PluginBase]],
        plugin_dependency_declarations: dict[str, list[Depends]],
        metadata_getter: Callable[[type[Any], str, str], Any]
    ) -> DependencyGraph[str]:
        """
        Build a dependency graph from registered plugins and their dependencies.

        Args:
            registered_item_classes: Map of registration names to plugin classes
            plugin_dependency_declarations: Map of registration names to their dependencies
            metadata_getter: Function to get plugin metadata attributes

        Returns:
            A constructed dependency graph
        """
        graph = DependencyGraph[str]()  # Nodes are registration_names in current scope

        # Add all registered plugins as nodes
        for reg_name in registered_item_classes:
            graph.add_node(reg_name)

        # Add dependency edges
        for dependent_reg_name, depends_list in plugin_dependency_declarations.items():
            for dep_obj in depends_list:
                prerequisite_reg_name: str
                if isinstance(dep_obj.plugin_identifier, str):
                    prerequisite_reg_name = dep_obj.plugin_identifier
                else:  # It's a Type
                    prerequisite_reg_name = metadata_getter(
                        dep_obj.plugin_identifier, "_plugginger_plugin_name", "dependency_resolution"
                    )
                graph.add_dependency_edge(
                    dependent_node=dependent_reg_name,
                    prerequisite_node=prerequisite_reg_name
                )

        self._logger.debug(f"Dependency graph constructed with {len(graph)} nodes.")
        return graph

    def validate_graph_and_resolve_order(
        self,
        graph: DependencyGraph[str],
        registered_item_classes: dict[str, type[PluginBase]],
        version_resolver: VersionResolverFunc,
        class_resolver: ClassResolverFunc,
        dependency_declarations: dict[str, list[Depends]]
    ) -> list[str]:
        """
        Validate the dependency graph and determine plugin instantiation order.

        Performs comprehensive validation including structure checks, cycle detection,
        version constraint validation, and signature validation.

        Args:
            graph: The dependency graph to validate
            registered_item_classes: Map of registration names to plugin classes
            version_resolver: Function to resolve plugin versions by registration name
            class_resolver: Function to resolve plugin classes by registration name
            dependency_declarations: Map of registration names to their dependencies

        Returns:
            List of registration names in topological order for instantiation

        Raises:
            MissingDependencyError: If required dependencies are missing
            CircularDependencyError: If circular dependencies are detected
            DependencyVersionConflictError: If version constraints are violated
            DependencyError: If other dependency validation fails
        """
        # Import here to avoid circular import
        from plugginger._internal.validation.dependency_validation import DependencyValidator

        validator = DependencyValidator(
            version_resolver_func=version_resolver,
            class_resolver_func=class_resolver
        )

        try:
            # Validate graph structure
            validator.validate_graph_structure(graph, set(registered_item_classes.keys()))

            # Get topological order (also detects cycles)
            sorted_registration_names: list[str] = graph.topological_sort()

            # Validate dependency versions and signatures
            validator.validate_dependency_versions_and_signatures(dependency_declarations)

            self._logger.info(f"Dependency validation successful. Plugin setup order: {sorted_registration_names}")
            return sorted_registration_names

        except (MissingDependencyError, CircularDependencyError, DependencyVersionConflictError, DependencyError) as e:
            self._logger.error(f"Dependency validation failed: {e!r}", exc_info=True)
            raise
