# src/plugginger/_internal/validation/dependency_validation.py

"""
Validation related to plugin dependencies, versions, and injection signatures.
"""

from __future__ import annotations

import inspect
from collections.abc import Callable

from packaging.specifiers import InvalidSpecifier, SpecifierSet
from packaging.version import InvalidVersion, Version

from plugginger._internal.graph import DependencyGraph
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import (
    ConfigurationError,
    DependencyError,
    DependencyVersionConflictError,
    MissingDependencyError,
    PluginRegistrationError,
)
from plugginger.core.semver import SemVerConstraint, VersionConflictResolver

# Type aliases for resolver functions
VersionResolverFunc = Callable[[str], str]
ClassResolverFunc = Callable[[str], type[PluginBase]]


class DependencyValidator:
    """
    Validator for plugin dependencies, versions, and injection signatures.

    This class provides comprehensive validation of plugin dependency graphs,
    including structure validation, version constraint checking, and dependency
    injection signature validation.

    Attributes:
        _version_resolver: Function to resolve plugin versions by name
        _class_resolver: Function to resolve plugin classes by name
    """

    def __init__(
        self,
        version_resolver_func: VersionResolverFunc,
        class_resolver_func: ClassResolverFunc,
    ) -> None:
        """
        Initialize the dependency validator.

        Args:
            version_resolver_func: Function that takes a plugin name and returns its version
            class_resolver_func: Function that takes a plugin name and returns its class
        """
        self._version_resolver = version_resolver_func
        self._class_resolver = class_resolver_func
        self._conflict_resolver = VersionConflictResolver()

    def validate_graph_structure(
        self,
        graph: DependencyGraph[str],
        all_registered_plugin_names: set[str],
    ) -> None:
        """
        Validate that all dependencies in the graph reference existing plugins.

        This method checks that every prerequisite node referenced in dependency
        edges actually exists in the set of registered plugins.

        Args:
            graph: Dependency graph to validate
            all_registered_plugin_names: Set of all registered plugin names

        Raises:
            MissingDependencyError: If any dependency references a non-existent plugin
        """
        for node in graph.get_all_nodes():
            prerequisites = graph.get_prerequisites(node)
            for prerequisite in prerequisites:
                if prerequisite not in all_registered_plugin_names:
                    raise MissingDependencyError(
                        f"Plugin '{node}' depends on '{prerequisite}' which is not registered"
                    )

    def validate_dependency_versions_and_signatures(
        self,
        plugin_dependency_declarations: dict[str, list[Depends]],
    ) -> None:
        """
        Validate dependency versions and injection signatures.

        This method performs comprehensive validation of plugin dependencies:
        1. Version constraint validation against actual plugin versions (with SemVer support)
        2. Global conflict detection across all plugins
        3. Dependency injection signature validation
        4. Completeness check for required constructor parameters

        Args:
            plugin_dependency_declarations: Mapping of plugin names to their dependency lists

        Raises:
            DependencyVersionConflictError: If version constraints are not satisfied
            DependencyError: If injection signatures are invalid or incomplete
            PluginRegistrationError: If plugin versions are invalid
            ConfigurationError: If version constraints are malformed
        """
        # Phase 1: Collect all version constraints for global conflict detection
        for dependent_plugin_name, depends_list in plugin_dependency_declarations.items():
            for dep_obj in depends_list:
                dependency_target_name = dep_obj.plugin_identifier

                # Add constraint to global resolver (supports both new 'version' and legacy 'version_constraint')
                version_constraint = dep_obj.version or dep_obj.version_constraint
                if version_constraint is not None:
                    self._conflict_resolver.add_constraint(
                        dependency_target_name, dependent_plugin_name, version_constraint
                    )

        # Phase 2: Build service versions mapping and check for conflicts
        service_versions = {}
        for _dependent_plugin_name, depends_list in plugin_dependency_declarations.items():
            for dep_obj in depends_list:
                dependency_target_name = dep_obj.plugin_identifier
                if dependency_target_name not in service_versions:
                    try:
                        actual_version = self._version_resolver(dependency_target_name)
                        service_versions[dependency_target_name] = actual_version
                    except Exception as e:
                        if not dep_obj.optional:
                            raise DependencyError(
                                f"Cannot resolve version for required plugin '{dependency_target_name}'"
                            ) from e

        # Check for global version conflicts
        conflicts = self._conflict_resolver.check_conflicts(service_versions)
        if conflicts:
            conflict_details = "\n".join(f"  - {conflict}" for conflict in conflicts)
            raise DependencyVersionConflictError(
                f"Version conflicts detected:\n{conflict_details}"
            )

        # Phase 3: Individual dependency validation and signature checking
        for dependent_plugin_name, depends_list in plugin_dependency_declarations.items():
            # Get the dependent plugin class for signature validation
            try:
                dependent_plugin_class = self._class_resolver(dependent_plugin_name)
            except Exception as e:
                raise DependencyError(
                    f"Cannot resolve class for plugin '{dependent_plugin_name}'"
                ) from e

            # Validate each dependency individually (for detailed error messages)
            for dep_obj in depends_list:
                dependency_target_name = dep_obj.plugin_identifier

                # Version constraint validation (with enhanced SemVer support)
                version_constraint = dep_obj.version or dep_obj.version_constraint
                if version_constraint is not None:
                    # Skip validation for optional dependencies that don't exist
                    if dep_obj.optional and dependency_target_name not in service_versions:
                        continue

                    self._validate_version_constraint_semver(
                        dependent_plugin_name, dependency_target_name, version_constraint
                    )

            # Validate dependency injection signatures
            self._validate_injection_signature(
                dependent_plugin_name, dependent_plugin_class, depends_list
            )

    def _validate_version_constraint(
        self,
        dependent_plugin_name: str,
        dependency_target_name: str,
        version_constraint: str,
    ) -> None:
        """
        Validate a version constraint against the actual plugin version.

        Args:
            dependent_plugin_name: Name of the plugin declaring the dependency
            dependency_target_name: Name of the dependency plugin
            version_constraint: Version constraint string (e.g., ">=1.0,<2.0")

        Raises:
            DependencyVersionConflictError: If version constraint is not satisfied
            PluginRegistrationError: If plugin version is invalid
            ConfigurationError: If version constraint is malformed
        """
        # Get actual version of the dependency
        try:
            actual_version_str = self._version_resolver(dependency_target_name)
        except Exception as e:
            raise DependencyError(
                f"Cannot resolve version for plugin '{dependency_target_name}'"
            ) from e

        # Parse actual version
        try:
            actual_version = Version(actual_version_str)
        except InvalidVersion as e:
            raise PluginRegistrationError(
                f"Plugin '{dependency_target_name}' has invalid version '{actual_version_str}'"
            ) from e

        # Parse version constraint
        try:
            specifier_set = SpecifierSet(version_constraint)
        except InvalidSpecifier as e:
            raise ConfigurationError(
                f"Plugin '{dependent_plugin_name}' has invalid version constraint "
                f"'{version_constraint}' for dependency '{dependency_target_name}'"
            ) from e

        # Check if actual version satisfies constraint
        if actual_version not in specifier_set:
            raise DependencyVersionConflictError(
                f"Plugin '{dependent_plugin_name}' requires '{dependency_target_name}' "
                f"version '{version_constraint}', but version '{actual_version_str}' is available"
            )

    def _validate_version_constraint_semver(
        self,
        dependent_plugin_name: str,
        dependency_target_name: str,
        version_constraint: str,
    ) -> None:
        """
        Validate a version constraint using enhanced SemVer support.

        This method supports both standard PEP 440 constraints and npm-style
        operators (^, ~) for more intuitive semantic versioning.

        Args:
            dependent_plugin_name: Name of the plugin declaring the dependency
            dependency_target_name: Name of the dependency plugin
            version_constraint: Version constraint string (e.g., ">=1.0.0,<2.0.0", "^1.2.0", "~1.2.0")

        Raises:
            DependencyVersionConflictError: If version constraint is not satisfied
            PluginRegistrationError: If plugin version is invalid
            ConfigurationError: If version constraint is malformed
        """
        # Get actual version of the dependency
        try:
            actual_version_str = self._version_resolver(dependency_target_name)
        except Exception as e:
            raise DependencyError(
                f"Cannot resolve version for plugin '{dependency_target_name}'"
            ) from e

        # Use SemVer constraint for enhanced validation
        try:
            constraint = SemVerConstraint(version_constraint)
        except ConfigurationError as e:
            raise ConfigurationError(
                f"Plugin '{dependent_plugin_name}' has invalid version constraint "
                f"'{version_constraint}' for dependency '{dependency_target_name}': {e}"
            ) from e

        # Check if actual version satisfies constraint
        try:
            if not constraint.satisfies(actual_version_str):
                raise DependencyVersionConflictError(
                    f"Plugin '{dependent_plugin_name}' requires '{dependency_target_name}' "
                    f"version '{version_constraint}', but version '{actual_version_str}' is available"
                )
        except PluginRegistrationError as e:
            raise PluginRegistrationError(
                f"Plugin '{dependency_target_name}' has invalid version '{actual_version_str}': {e}"
            ) from e

    def _validate_injection_signature(
        self,
        dependent_plugin_name: str,
        dependent_plugin_class: type[PluginBase],
        depends_list: list[Depends],
    ) -> None:
        """
        Validate dependency injection signature for a plugin.

        This method checks that:
        1. All declared dependencies have corresponding __init__ parameters
        2. All required __init__ parameters are covered by dependencies

        Args:
            dependent_plugin_name: Name of the plugin being validated
            dependent_plugin_class: Class of the plugin being validated
            depends_list: List of declared dependencies

        Raises:
            DependencyError: If injection signature is invalid or incomplete
        """
        # Inspect __init__ method signature
        try:
            init_signature = inspect.signature(dependent_plugin_class.__init__)
        except Exception as e:
            raise DependencyError(
                f"Cannot inspect __init__ signature for plugin '{dependent_plugin_name}'"
            ) from e

        # Get parameter information (excluding 'self' and 'app')
        init_params = {}
        for param_name, param in init_signature.parameters.items():
            if param_name not in ('self', 'app'):
                init_params[param_name] = param

        # Create mapping of dependency names to their targets
        dependency_names = {dep.plugin_identifier for dep in depends_list}

        # Check if plugin uses **injected_dependencies pattern
        has_var_keyword = any(
            param.kind == inspect.Parameter.VAR_KEYWORD
            for param in init_params.values()
        )

        # If using **injected_dependencies, skip explicit parameter validation
        if has_var_keyword:
            # Dependencies will be injected via **kwargs and set as attributes
            return

        # Check that all declared dependencies have corresponding parameters (legacy mode)
        for dep in depends_list:
            dependency_name = dep.plugin_identifier
            if dependency_name not in init_params:
                raise DependencyError(
                    f"Plugin '{dependent_plugin_name}' declares dependency '{dependency_name}' "
                    f"but __init__ method has no parameter '{dependency_name}'"
                )

        # Check that all required parameters are covered by dependencies (legacy mode only)
        if not has_var_keyword:
            for param_name, param in init_params.items():
                # Skip **kwargs parameters (like **injected_dependencies)
                if param.kind == inspect.Parameter.VAR_KEYWORD:
                    continue

                if param.default is inspect.Parameter.empty:  # Required parameter
                    if param_name not in dependency_names:
                        raise DependencyError(
                            f"Plugin '{dependent_plugin_name}' __init__ requires parameter '{param_name}' "
                            f"but no dependency is declared for it"
                        )
