name: Plugginger CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached dependencies
      id: cached-poetry-dependencies
      uses: actions/cache@v4
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --with dev

    - name: Install project
      run: poetry install --no-interaction

    - name: Code Quality - Ruff Check
      run: |
        echo "::group::<PERSON><PERSON>"
        poetry run ruff check . --output-format=github
        echo "::endgroup::"

    - name: Code Quality - Ruff Format Check
      run: |
        echo "::group::Ruff Format Check"
        poetry run ruff format --check .
        echo "::endgroup::"

    - name: Type Safety - MyPy
      run: |
        echo "::group::MyPy Type Checking"
        poetry run mypy --strict . --show-error-codes
        echo "::endgroup::"

    - name: Test Suite
      run: |
        echo "::group::Running Test Suite"
        poetry run pytest tests/ -v --tb=short --cov=src --cov-report=xml --cov-report=term-missing
        echo "::endgroup::"

    - name: Coverage Check
      run: |
        echo "::group::Coverage Analysis"
        poetry run coverage report --show-missing --fail-under=75
        echo "::endgroup::"

    - name: Upload coverage to Codecov
      if: matrix.python-version == '3.11'
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        token: ${{ secrets.CODECOV_TOKEN }}
        fail_ci_if_error: false

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install Poetry
      uses: snok/install-poetry@v1

    - name: Install dependencies
      run: poetry install --with security

    - name: Security - Bandit
      run: |
        echo "::group::Bandit Security Scan"
        poetry run bandit -r src/ -f json -o bandit-report.json || echo "Bandit found issues"
        echo "::endgroup::"

    - name: Security - Safety
      run: |
        echo "::group::Safety Vulnerability Check"
        poetry run safety check --json --output safety-report.json || echo "Safety found issues"
        echo "::endgroup::"

  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    needs: [quality-gates]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install Poetry
      uses: snok/install-poetry@v1

    - name: Build package
      run: |
        echo "::group::Building Package"
        poetry build
        echo "::endgroup::"

    - name: Verify build artifacts
      run: |
        echo "::group::Verifying Build Artifacts"
        ls -la dist/
        echo "::endgroup::"

    # PyPI publishing only on tags - not on every main push
    # - name: Publish to PyPI
    #   if: startsWith(github.ref, 'refs/tags/')
    #   env:
    #     POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_TOKEN }}
    #   run: poetry publish
